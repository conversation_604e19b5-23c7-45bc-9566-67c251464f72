import React from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

interface LoadingSpinnerProps {
  tip?: string;
  size?: 'small' | 'default' | 'large';
  fullScreen?: boolean;
}

interface CSSProperties {
  [key: string]: string | number;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  tip = '加载中...', 
  size = 'large',
  fullScreen = false
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 40 : 24 }} spin />;
  
  const containerStyle: CSSProperties = fullScreen ? {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    width: '100vw',
    position: 'fixed',
    top: 0,
    left: 0,
    background: 'rgba(255, 255, 255, 0.8)',
    zIndex: 9999
  } : {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '40px 0'
  };

  return (
    <div style={containerStyle}>
      <Spin indicator={antIcon} size={size} tip={tip} />
    </div>
  );
};

export default LoadingSpinner; 