version: '3.8'

services:
  frontend:
    image: node:18-alpine
    container_name: aggregator-web-frontend
    working_dir: /app
    volumes:
      - .:/app
    command: sh -c "npm install --legacy-peer-deps && npm run build"
    environment:
      - NODE_ENV=production
    networks:
      - aggregator-network
    healthcheck:
      test: ["CMD", "ls", "build"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  backend:
    image: python:3.9-slim
    container_name: aggregator-web-backend
    working_dir: /app
    volumes:
      - .:/app
      - ..:/aggregator
    command: sh -c "cd backend && pip install -r requirements.txt && cd .. && python backend/main.py"
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/aggregator
    depends_on:
      frontend:
        condition: service_healthy
    networks:
      - aggregator-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

networks:
  aggregator-network:
    driver: bridge