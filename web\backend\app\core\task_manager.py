#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import json
import os
import subprocess
import time
import uuid
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any
import threading
import queue

from dataclasses import dataclass, field
from pydantic import BaseModel


class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(str, Enum):
    COLLECT = "collect"
    PROCESS = "process"
    CUSTOM = "custom"


@dataclass
class TaskConfig:
    """任务配置"""
    task_type: TaskType
    name: str
    script_path: str
    args: List[str] = field(default_factory=list)
    env_vars: Dict[str, str] = field(default_factory=dict)
    working_dir: Optional[str] = None
    timeout: int = 3600  # 默认1小时超时


class TaskInfo(BaseModel):
    """任务信息模型"""
    id: str
    name: str
    task_type: TaskType
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    log_output: List[str] = []
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    # 执行配置信息
    script_path: Optional[str] = None
    args: List[str] = []
    env_vars: Dict[str, str] = {}
    timeout: int = 3600


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.running_processes: Dict[str, subprocess.Popen] = {}
        self.log_queues: Dict[str, queue.Queue] = {}
        self.project_root = Path(__file__).parent.parent.parent.parent.parent
        
    def create_task(self, config: TaskConfig) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        task_info = TaskInfo(
            id=task_id,
            name=config.name,
            task_type=config.task_type,
            status=TaskStatus.PENDING,
            created_at=datetime.now(),
            # 保存执行配置
            script_path=config.script_path,
            args=config.args,
            env_vars=config.env_vars,
            timeout=config.timeout
        )
        
        self.tasks[task_id] = task_info
        self.log_queues[task_id] = queue.Queue()
        
        return task_id
    
    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[TaskInfo]:
        """获取所有任务"""
        return list(self.tasks.values())
    
    def get_recent_tasks(self, limit: int = 10) -> List[TaskInfo]:
        """获取最近的任务"""
        sorted_tasks = sorted(
            self.tasks.values(), 
            key=lambda x: x.created_at, 
            reverse=True
        )
        return sorted_tasks[:limit]
    
    async def execute_task(self, task_id: str, config: TaskConfig) -> bool:
        """异步执行任务"""
        if task_id not in self.tasks:
            return False
            
        task = self.tasks[task_id]
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        
        try:
            # 构建命令
            cmd = ["python", config.script_path] + config.args
            
            # 设置工作目录
            working_dir = config.working_dir or str(self.project_root)
            
            # 对于自定义任务，强制使用subscribe目录
            if config.task_type == TaskType.CUSTOM:
                working_dir = str(self.project_root / "subscribe")
                print(f"自定义任务强制使用工作目录: {working_dir}")
            
            # 设置环境变量
            env = os.environ.copy()
            env.update(config.env_vars)
            
            # 添加网络超时设置
            env.update({
                'HTTP_TIMEOUT': '30',  # HTTP请求超时30秒
                'CONNECT_TIMEOUT': '10',  # 连接超时10秒
                'READ_TIMEOUT': '60',  # 读取超时60秒
            })
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=working_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.running_processes[task_id] = process
            
            # 启动日志读取线程
            log_thread = threading.Thread(
                target=self._read_process_output,
                args=(task_id, process)
            )
            log_thread.daemon = True
            log_thread.start()
            
            # 等待进程完成，带超时控制
            try:
                return_code = await asyncio.wait_for(
                    asyncio.get_event_loop().run_in_executor(None, process.wait),
                    timeout=config.timeout
                )
                
                # 更新任务状态
                if return_code == 0:
                    task.status = TaskStatus.COMPLETED
                    task.progress = 100.0
                else:
                    task.status = TaskStatus.FAILED
                    task.error_message = f"进程退出码: {return_code}"
                    
            except asyncio.TimeoutError:
                # 超时处理
                task.status = TaskStatus.FAILED
                task.error_message = f"任务执行超时 ({config.timeout}秒)，已强制终止"
                
                # 强制终止进程
                try:
                    process.terminate()
                    await asyncio.sleep(5)  # 等待5秒
                    if process.poll() is None:
                        process.kill()  # 强制杀死
                except Exception:
                    pass
                    
                return_code = -1
                
            task.completed_at = datetime.now()
            
            # 清理
            if task_id in self.running_processes:
                del self.running_processes[task_id]
                
            return return_code == 0
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            task.status = TaskStatus.FAILED
            task.error_message = f"任务执行异常: {str(e)}"
            task.completed_at = datetime.now()

            # 记录详细错误信息到日志
            if task_id in self.log_queues:
                try:
                    self.log_queues[task_id].put_nowait({
                        'type': 'error',
                        'message': f"任务执行失败: {str(e)}",
                        'details': error_details,
                        'timestamp': datetime.now().isoformat()
                    })
                except queue.Full:
                    pass

            print(f"Task {task_id} failed with error: {error_details}")

            # 清理
            if task_id in self.running_processes:
                del self.running_processes[task_id]

            return False
    
    def _read_process_output(self, task_id: str, process: subprocess.Popen):
        """读取进程输出"""
        task = self.tasks.get(task_id)
        if not task:
            return
            
        try:
            for line in iter(process.stdout.readline, ''):
                if line:
                    log_line = line.strip()
                    task.log_output.append(log_line)
                    
                    # 将日志放入队列供WebSocket使用
                    if task_id in self.log_queues:
                        try:
                            self.log_queues[task_id].put_nowait({
                                'type': 'log',
                                'message': log_line,
                                'timestamp': datetime.now().isoformat()
                            })
                        except queue.Full:
                            pass  # 队列满了就丢弃
                            
                    # 简单的进度估算（基于日志关键词）
                    if any(keyword in log_line.lower() for keyword in ['progress', 'finished', 'completed']):
                        task.progress = min(task.progress + 10, 90)
                        
        except Exception as e:
            if task_id in self.log_queues:
                try:
                    self.log_queues[task_id].put_nowait({
                        'type': 'error',
                        'message': f"读取日志失败: {str(e)}",
                        'timestamp': datetime.now().isoformat()
                    })
                except queue.Full:
                    pass
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False
            
        task = self.tasks[task_id]
        
        if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            return False
            
        # 终止进程
        if task_id in self.running_processes:
            try:
                process = self.running_processes[task_id]
                process.terminate()
                
                # 等待一段时间后强制杀死
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    
                del self.running_processes[task_id]
            except Exception:
                pass
        
        # 更新状态
        task.status = TaskStatus.CANCELLED
        task.completed_at = datetime.now()
        
        return True
    
    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        if task_id not in self.tasks:
            return False
            
        task = self.tasks[task_id]
        
        # 如果任务正在运行，先取消它
        if task.status == TaskStatus.RUNNING:
            self.cancel_task(task_id)
        
        # 清理资源
        if task_id in self.running_processes:
            del self.running_processes[task_id]
        if task_id in self.log_queues:
            del self.log_queues[task_id]
        
        # 删除任务记录
        del self.tasks[task_id]
        
        return True
    
    def get_task_logs(self, task_id: str, since: Optional[int] = None) -> List[str]:
        """获取任务日志"""
        task = self.tasks.get(task_id)
        if not task:
            return []
            
        logs = task.log_output
        if since is not None:
            logs = logs[since:]
            
        return logs
    
    def get_log_stream(self, task_id: str) -> queue.Queue:
        """获取日志流队列"""
        return self.log_queues.get(task_id, queue.Queue())
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理完成的任务"""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        
        task_ids_to_remove = []
        for task_id, task in self.tasks.items():
            if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] 
                and task.completed_at 
                and task.completed_at.timestamp() < cutoff_time):
                task_ids_to_remove.append(task_id)
        
        for task_id in task_ids_to_remove:
            del self.tasks[task_id]
            if task_id in self.log_queues:
                del self.log_queues[task_id]


# 全局任务管理器实例
task_manager = TaskManager()


def get_task_manager() -> TaskManager:
    """获取任务管理器实例"""
    return task_manager


def create_collect_task(
    name: str = "代理收集任务",
    all_config: bool = True,
    overwrite: bool = False,
    skip_check: bool = True,
    **kwargs
) -> str:
    """创建collect任务的便捷函数"""
    args = []
    env_vars = {}
    
    if all_config:
        args.append("--all")
    if overwrite:
        args.append("--overwrite")
    if skip_check:
        args.append("--skip")
        

        
    # 添加其他参数
    for key, value in kwargs.items():
        if key == "num" and value:
            args.extend(["--num", str(value)])
        elif key == "delay" and value:
            args.extend(["--delay", str(value)])
        elif key == "gist" and value:
            args.extend(["--gist", str(value)])
        elif key == "key" and value:
            args.extend(["--key", str(value)])
    
    config = TaskConfig(
        task_type=TaskType.COLLECT,
        name=name,
        script_path="subscribe/collect.py",
        args=args,
        env_vars=env_vars
    )
    
    return task_manager.create_task(config)


def create_process_task(
    name: str = "代理处理任务",
    overwrite: bool = False,
    server_config: str = "",
    **kwargs
) -> str:
    """创建process任务的便捷函数"""
    args = []
    env_vars = {}
    
    if overwrite:
        args.append("--overwrite")
    if server_config:
        args.extend(["--server", server_config])
        

        
    # 添加其他参数
    for key, value in kwargs.items():
        if key == "num" and value:
            args.extend(["--num", str(value)])
        elif key == "timeout" and value:
            args.extend(["--timeout", str(value)])
        elif key == "retry" and value:
            args.extend(["--retry", str(value)])
    
    config = TaskConfig(
        task_type=TaskType.PROCESS,
        name=name,
        script_path="subscribe/process.py", 
        args=args,
        env_vars=env_vars
    )
    
    return task_manager.create_task(config)


def create_custom_task(
    name: str = "自定义任务",
    code: str = "",
    command: str = "",
    timeout: int = 300,  # 5分钟默认超时
    **kwargs
) -> str:
    """创建自定义任务的便捷函数"""
    import tempfile
    import os
    
    args = []
    env_vars = {}
    script_path = ""
    
    # 强制设置工作目录为subscribe目录
    working_dir = str(Path(__file__).parent.parent.parent.parent.parent / "subscribe")
    
    if code:
        # 如果提供了代码，创建临时Python文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            # 添加一些安全限制和常用导入
            safe_code = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import json
import time
from datetime import datetime
from pathlib import Path

# 强制将工作目录设置为subscribe目录 - 使用跨平台路径处理
import os
working_dir_normalized = os.path.normpath(r"{working_dir}")
os.chdir(working_dir_normalized)
print(f"[{{datetime.now()}}] 工作目录已设置为: {{os.getcwd()}}")

# 限制文件操作只能在subscribe目录内
import builtins
original_open = builtins.open

def safe_open(file, mode='r', **kwargs):
    # 安全检查，防止访问系统关键文件和限制只能在subscribe目录下操作
    if isinstance(file, str):
        file_path = os.path.abspath(file)
        subscribe_dir_abs = os.path.abspath(os.path.normpath(r"{working_dir}"))

        # 检查是否在subscribe目录下
        if not file_path.startswith(subscribe_dir_abs):
            raise PermissionError(f"只允许访问subscribe目录下的文件: {{file_path}}")

        # 检查是否是系统关键文件 - 跨平台处理
        import platform
        if platform.system() == "Windows":
            forbidden_paths = ['C:\\Windows', 'C:\\System32']
        else:
            forbidden_paths = ['/etc', '/proc', '/sys', '/dev']

        if any(file_path.startswith(fp) for fp in forbidden_paths):
            raise PermissionError("Access to system files is not allowed")
    return original_open(file, mode, **kwargs)

builtins.open = safe_open

# 限制os.path操作
original_listdir = os.listdir
def safe_listdir(path='.'):
    path = os.path.abspath(path)
    subscribe_dir_abs = os.path.abspath(os.path.normpath(r"{working_dir}"))
    if not path.startswith(subscribe_dir_abs):
        raise PermissionError(f"只允许访问subscribe目录下的内容: {{path}}")
    return original_listdir(path)
os.listdir = safe_listdir

print(f"[{{datetime.now()}}] 开始执行自定义代码...")

# 使用exec执行用户代码，避免缩进问题
user_code = '''
{code}
'''

try:
    exec(user_code)
    print(f"[{{datetime.now()}}] 自定义代码执行完成")
except Exception as e:
    print(f"[{{datetime.now()}}] 执行错误: {{e}}")
    import traceback
    traceback.print_exc()
"""
            f.write(safe_code)
            script_path = f.name
            
        # 设置超时
        env_vars["SCRIPT_TIMEOUT"] = str(timeout)
        
    elif command:
        # 如果提供了命令，创建临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            wrapper_code = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os
from datetime import datetime

# 强制将工作目录设置为subscribe目录 - 使用跨平台路径处理
subscribe_dir = os.path.normpath(r"{working_dir}")
os.chdir(subscribe_dir)
print(f"[{{datetime.now()}}] 工作目录已设置为: {{os.getcwd()}}")

print(f"[{{datetime.now()}}] 开始执行自定义命令: {command}")

try:
    # 执行自定义命令
    result = subprocess.run(
        "{command}",
        shell=True,
        capture_output=True,
        text=True,
        timeout={timeout},
        cwd=subscribe_dir  # 强制在subscribe目录下执行
    )
    
    print(f"[{{datetime.now()}}] 命令执行完成，返回码: {{result.returncode}}")
    
    if result.stdout:
        print("=== 标准输出 ===")
        print(result.stdout)
        
    if result.stderr:
        print("=== 错误输出 ===")
        print(result.stderr)
        
    sys.exit(result.returncode)
    
except subprocess.TimeoutExpired:
    print(f"[{{datetime.now()}}] 命令执行超时")
    sys.exit(1)
except Exception as e:
    print(f"[{{datetime.now()}}] 执行错误: {{e}}")
    sys.exit(1)
"""
            f.write(wrapper_code)
            script_path = f.name
    else:
        raise ValueError("必须提供代码或命令")
    
    config = TaskConfig(
        task_type=TaskType.CUSTOM,
        name=name,
        script_path=script_path,
        args=args,
        env_vars=env_vars,
        timeout=timeout,  # 保持秒为单位，与其他地方保持一致
        working_dir=working_dir  # 强制设置工作目录为subscribe目录
    )
    
    return task_manager.create_task(config) 