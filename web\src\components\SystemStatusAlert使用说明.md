# SystemStatusAlert 组件使用说明

## 概述
`SystemStatusAlert` 是一个灵活的系统状态显示组件，可以根据任务状态动态显示不同的系统状态信息。

## 功能特性
- 🎯 **智能状态检测** - 根据任务状态自动判断系统状态
- 🎨 **动态样式** - 不同状态使用不同的颜色和图标
- 🔧 **高度可定制** - 支持自定义消息、描述和操作按钮
- 📱 **响应式设计** - 支持不同尺寸显示
- ♻️ **可复用** - 可在多个页面和组件中使用

## 状态类型
1. **成功状态** (success) - 系统运行正常
2. **信息状态** (info) - 有任务正在运行
3. **警告状态** (warning) - 有已取消的任务
4. **错误状态** (error) - 有失败的任务

## 基本用法

### 1. 完整功能版本（Dashboard页面）
```tsx
<SystemStatusAlert
  tasks={tasks}
  showActions={true}
  onQuickCollect={() => quickExecute('collect')}
  onQuickProcess={() => quickExecute('process')}
  collectLoading={quickExecuteLoading.collect}
  processLoading={quickExecuteLoading.process}
  size="default"
/>
```

### 2. 简化版本（卡片内显示）
```tsx
<SystemStatusAlert
  tasks={tasks}
  showActions={false}
  size="small"
  customDescription="点击上方按钮可快速执行原项目功能"
/>
```

### 3. 自定义消息版本
```tsx
<SystemStatusAlert
  tasks={tasks}
  showActions={false}
  customMessage="自定义状态消息"
  customDescription="自定义描述信息"
  size="small"
/>
```

### 4. 仅显示状态（无任务数据）
```tsx
<SystemStatusAlert
  showActions={false}
  customMessage="系统运行正常"
  customDescription="所有核心服务正常运行"
/>
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `tasks` | `any[]` | `[]` | 任务列表，用于判断系统状态 |
| `showActions` | `boolean` | `true` | 是否显示操作按钮 |
| `onQuickCollect` | `() => void` | - | 快速收集按钮点击回调 |
| `onQuickProcess` | `() => void` | - | 快速处理按钮点击回调 |
| `collectLoading` | `boolean` | `false` | 收集按钮加载状态 |
| `processLoading` | `boolean` | `false` | 处理按钮加载状态 |
| `size` | `'small' \| 'default' \| 'large'` | `'default'` | 组件尺寸 |
| `customMessage` | `string` | - | 自定义状态消息 |
| `customDescription` | `string` | - | 自定义描述信息 |

## 状态判断逻辑

```typescript
// 状态优先级（从高到低）：
1. 有失败任务 → 错误状态 (error)
2. 有取消任务且无运行任务 → 警告状态 (warning)  
3. 有运行中任务 → 信息状态 (info)
4. 默认 → 成功状态 (success)
```

## 样式定制

组件会根据状态自动应用相应的颜色主题：
- **成功**: 绿色主题 (#52c41a)
- **信息**: 蓝色主题 (#1890ff)
- **警告**: 橙色主题 (#faad14)
- **错误**: 红色主题 (#ff4d4f)

## 在其他页面中使用

```tsx
// 1. 导入组件
import SystemStatusAlert from '../components/SystemStatusAlert';

// 2. 在组件中使用
const MyPage: React.FC = () => {
  const [tasks, setTasks] = useState([]);
  
  return (
    <div>
      <SystemStatusAlert
        tasks={tasks}
        showActions={false}
        size="small"
      />
    </div>
  );
};
```

## 扩展功能

如需添加新的状态类型或修改判断逻辑，可以修改 `getSystemStatusInfo` 函数：

```typescript
// 在 SystemStatusAlert.tsx 中修改
export const getSystemStatusInfo = (tasks: any[] = []): SystemStatusInfo => {
  // 添加自定义状态判断逻辑
  // ...
};
```
