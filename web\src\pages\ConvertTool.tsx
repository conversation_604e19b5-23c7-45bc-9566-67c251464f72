import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Typography,
  Alert,
  Row,
  Col,
  Divider,
  Switch,
  Collapse,
  Tag,
  Result,
  Spin,
  App,
} from 'antd';
import {
  TranslationOutlined,
  DownloadOutlined,
  CopyOutlined,
  ReloadOutlined,
  SettingOutlined,
  LinkOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';

const { Option } = Select;
const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface ConvertConfig {
  emoji: boolean;
  list: boolean;
  udp: boolean;
  tfo: boolean;
  scv: boolean;
  fdn: boolean;
  new_name: boolean;
}

const ConvertTool: React.FC = () => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [supportedFormats, setSupportedFormats] = useState<string[]>([]);
  const [config, setConfig] = useState<ConvertConfig>({
    emoji: true,
    list: false,
    udp: true,
    tfo: false,
    scv: false,
    fdn: true,
    new_name: true,
  });

  useEffect(() => {
    fetchSupportedFormats();
  }, []);

  const fetchSupportedFormats = async () => {
    try {
      // 模拟获取支持的格式
      setSupportedFormats(['clash', 'v2ray', 'singbox', 'quantumult-x', 'surfboard']);
    } catch (error) {
      message.error('获取支持格式失败');
    }
  };

  const handleConvert = async (values: any) => {
    setLoading(true);
    try {
      // 模拟转换过程
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockResult = {
        id: `conv_${Date.now()}`,
        status: 'completed',
        input_url: values.url,
        target_format: values.target,
        output_url: `https://api.example.com/convert/${Date.now()}.yaml`,
        node_count: 156,
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
      };
      
      setResult(mockResult);
      message.success('转换完成！');
    } catch (error) {
      message.error('转换失败，请检查订阅链接是否有效');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    message.success('链接已复制到剪贴板');
  };

  const handleDownload = (url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success('下载已开始');
  };

  const formatDescriptions = {
    clash: 'Clash for Windows、Clash for Android 等客户端',
    v2ray: 'V2RayN、V2RayNG、V2RayX 等客户端',
    singbox: 'SingBox 客户端，新一代代理工具',
    'quantumult-x': 'Quantumult X，iOS 平台专业代理工具',
    surfboard: 'Surfboard，Android 平台 Surge 替代品',
  };

  const presetConfigs = [
    {
      name: '默认配置',
      description: '推荐设置，适合大多数用户',
      config: {
        emoji: true,
        list: false,
        udp: true,
        tfo: false,
        scv: false,
        fdn: true,
        new_name: true,
      },
    },
    {
      name: '精简配置',
      description: '最小化配置，减少文件大小',
      config: {
        emoji: false,
        list: true,
        udp: false,
        tfo: false,
        scv: false,
        fdn: false,
        new_name: false,
      },
    },
    {
      name: '完整配置',
      description: '启用所有功能，文件较大',
      config: {
        emoji: true,
        list: false,
        udp: true,
        tfo: true,
        scv: true,
        fdn: true,
        new_name: true,
      },
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Title level={3}>
            <TranslationOutlined /> 订阅转换工具
          </Title>
          <Text type="secondary">
            将订阅链接转换为不同客户端兼容的格式，支持 Clash、V2Ray、SingBox 等
          </Text>
        </div>

        <Row gutter={24}>
          <Col xs={24} lg={14}>
            <Card title="转换设置" size="small">
              <Form
                form={form}
                layout="vertical"
                onFinish={handleConvert}
                initialValues={{
                  target: 'clash',
                }}
              >
                <Form.Item
                  name="url"
                  label="订阅链接"
                  rules={[
                    { required: true, message: '请输入订阅链接' },
                    { type: 'url', message: '请输入有效的URL' },
                  ]}
                >
                  <TextArea
                    rows={3}
                    placeholder="输入要转换的订阅链接，支持多个链接（每行一个）"
                  />
                </Form.Item>

                <Form.Item
                  name="target"
                  label="目标格式"
                  rules={[{ required: true, message: '请选择目标格式' }]}
                >
                  <Select placeholder="选择转换目标格式">
                    {supportedFormats.map(format => (
                      <Option key={format} value={format}>
                        <Space>
                          <Tag color="blue">{format.toUpperCase()}</Tag>
                          <Text type="secondary">
                            {formatDescriptions[format as keyof typeof formatDescriptions]}
                          </Text>
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      icon={<TranslationOutlined />}
                    >
                      开始转换
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => {
                        form.resetFields();
                        setResult(null);
                      }}
                    >
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>

            {/* 转换结果 */}
            {loading && (
              <Card style={{ marginTop: 16 }}>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Spin size="large" />
                  <div style={{ marginTop: 16 }}>
                    <Text>正在转换订阅，请稍候...</Text>
                  </div>
                </div>
              </Card>
            )}

            {result && !loading && (
              <Card 
                title={
                  <Space>
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    转换完成
                  </Space>
                }
                style={{ marginTop: 16 }}
              >
                <div>
                  <Row gutter={[16, 16]}>
                    <Col span={8}>
                      <Text strong>转换格式:</Text>
                    </Col>
                    <Col span={16}>
                      <Tag color="blue">{result.target_format.toUpperCase()}</Tag>
                    </Col>
                    
                    <Col span={8}>
                      <Text strong>节点数量:</Text>
                    </Col>
                    <Col span={16}>
                      <Text type="success">{result.node_count} 个</Text>
                    </Col>
                    
                    <Col span={8}>
                      <Text strong>转换时间:</Text>
                    </Col>
                    <Col span={16}>
                      <Text type="secondary">
                        {new Date(result.completed_at).toLocaleString()}
                      </Text>
                    </Col>
                  </Row>

                  <Divider />

                  <div>
                    <Text strong>转换结果链接:</Text>
                    <div style={{ 
                      background: '#f5f5f5', 
                      padding: '12px', 
                      borderRadius: '6px',
                      margin: '8px 0',
                      wordBreak: 'break-all'
                    }}>
                      <Text code>{result.output_url}</Text>
                    </div>
                    
                    <Space>
                      <Button
                        type="primary"
                        icon={<CopyOutlined />}
                        onClick={() => handleCopyUrl(result.output_url)}
                      >
                        复制链接
                      </Button>
                      <Button
                        icon={<DownloadOutlined />}
                        onClick={() => handleDownload(result.output_url, `${result.target_format}.yaml`)}
                      >
                        下载文件
                      </Button>
                      <Button
                        icon={<LinkOutlined />}
                        onClick={() => window.open(result.output_url, '_blank')}
                      >
                        在新窗口打开
                      </Button>
                    </Space>
                  </div>
                </div>
              </Card>
            )}
          </Col>

          <Col xs={24} lg={10}>
            {/* 预设配置 */}
            <Card title="预设配置" size="small" style={{ marginBottom: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                {presetConfigs.map((preset, index) => (
                  <Card
                    key={index}
                    size="small"
                    hoverable
                    onClick={() => setConfig(preset.config)}
                    style={{ cursor: 'pointer' }}
                  >
                    <div>
                      <Text strong>{preset.name}</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {preset.description}
                      </Text>
                    </div>
                  </Card>
                ))}
              </Space>
            </Card>

            {/* 高级设置 */}
            <Card title="高级设置" size="small">
              <Collapse 
                ghost
                items={[
                  {
                    key: '1',
                    label: '转换选项',
                    children: (
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text>启用 Emoji</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              为节点名称添加国旗表情
                            </Text>
                          </div>
                          <Switch
                            checked={config.emoji}
                            onChange={(checked) => setConfig(prev => ({ ...prev, emoji: checked }))}
                          />
                        </div>

                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text>列表模式</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              生成节点列表而非配置文件
                            </Text>
                          </div>
                          <Switch
                            checked={config.list}
                            onChange={(checked) => setConfig(prev => ({ ...prev, list: checked }))}
                          />
                        </div>

                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text>启用 UDP</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              启用 UDP 转发功能
                            </Text>
                          </div>
                          <Switch
                            checked={config.udp}
                            onChange={(checked) => setConfig(prev => ({ ...prev, udp: checked }))}
                          />
                        </div>

                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text>TCP Fast Open</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              启用 TCP Fast Open 优化
                            </Text>
                          </div>
                          <Switch
                            checked={config.tfo}
                            onChange={(checked) => setConfig(prev => ({ ...prev, tfo: checked }))}
                          />
                        </div>

                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text>跳过证书验证</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              跳过 TLS 证书验证
                            </Text>
                          </div>
                          <Switch
                            checked={config.scv}
                            onChange={(checked) => setConfig(prev => ({ ...prev, scv: checked }))}
                          />
                        </div>

                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text>过滤非法节点</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              自动过滤无法解析的节点
                            </Text>
                          </div>
                          <Switch
                            checked={config.fdn}
                            onChange={(checked) => setConfig(prev => ({ ...prev, fdn: checked }))}
                          />
                        </div>

                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text>重命名节点</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              根据规则重命名节点
                            </Text>
                          </div>
                          <Switch
                            checked={config.new_name}
                            onChange={(checked) => setConfig(prev => ({ ...prev, new_name: checked }))}
                          />
                        </div>
                      </Space>
                    )
                  }
                ]}
              />
            </Card>
          </Col>
        </Row>

        {/* 使用说明 */}
        <Card title="使用说明" style={{ marginTop: 24 }}>
          <Row gutter={24}>
            <Col xs={24} md={8}>
              <div>
                <Title level={5}>支持的输入格式</Title>
                <ul>
                  <li>Clash 订阅链接</li>
                  <li>V2Ray 订阅链接</li>
                  <li>Shadowsocks 订阅链接</li>
                  <li>单个节点链接</li>
                  <li>多个链接（每行一个）</li>
                </ul>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div>
                <Title level={5}>输出格式说明</Title>
                <ul>
                  <li><strong>Clash:</strong> YAML 格式，适用于 Clash 系列客户端</li>
                  <li><strong>V2Ray:</strong> JSON 格式，适用于 V2Ray 系列客户端</li>
                  <li><strong>SingBox:</strong> JSON 格式，新一代代理工具</li>
                  <li><strong>Quantumult X:</strong> 适用于 iOS 客户端</li>
                  <li><strong>Surfboard:</strong> 适用于 Android 客户端</li>
                </ul>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div>
                <Title level={5}>注意事项</Title>
                <ul>
                  <li>转换过程可能需要几秒钟时间</li>
                  <li>请确保订阅链接有效且可访问</li>
                  <li>生成的链接有效期为 24 小时</li>
                  <li>不会保存您的原始订阅链接</li>
                  <li>转换结果支持大多数主流客户端</li>
                </ul>
              </div>
            </Col>
          </Row>
        </Card>
      </Card>
    </div>
  );
};

export default ConvertTool;