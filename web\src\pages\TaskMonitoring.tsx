import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Button,
  Table,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Progress,
  Modal,
  Form,
  Select,
  InputNumber,
  Input,
  Divider,
  Badge,
  Tooltip,
  Empty,
  Switch,
  Alert,
  Timeline,
  Drawer,
  App,
  Popconfirm,
  Tabs,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  DeleteOutlined,
  RocketOutlined,
  BugOutlined,
  HistoryOutlined,
  FileTextOutlined,
  CodeOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { tasksApi } from '../services/api';
import MonacoEditor from 'react-monaco-editor';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface Task {
  id: string;
  name: string;
  task_type: 'collect' | 'process' | 'custom';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  progress: number;
  log_output: string[];
  error_message?: string;
  result?: any;
}

interface TaskExecuteParams {
  all_config: boolean;
  overwrite: boolean;
  skip_check: boolean;
  skip_subconverter: boolean;
  num_threads: number;
  delay: number;
  gist: string;
  key: string;
  server_config: string;
  timeout: number;
  retry: number;
}

const TaskMonitoring: React.FC = () => {
  const { message } = App.useApp();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [quickExecuteModalVisible, setQuickExecuteModalVisible] = useState(false);
  const [taskDetailVisible, setTaskDetailVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [executeType, setExecuteType] = useState<'collect' | 'process' | 'custom'>('collect');
  const [form] = Form.useForm();
  const [executeForm] = Form.useForm();
  const [customCode, setCustomCode] = useState<string>('# Python代码示例\nprint("Hello, World!")');
  const [customCommand, setCustomCommand] = useState<string>('');
  const [codeTemplates] = useState([
    {
      name: '代理测试模板',
      code: `# 代理节点测试脚本
import requests
import time

def test_proxy(proxy_url):
    """测试代理连接"""
    try:
        response = requests.get(
            'http://httpbin.org/ip',
            proxies={'http': proxy_url, 'https': proxy_url},
            timeout=10
        )
        print(f"代理测试成功: {response.json()}")
        return True
    except Exception as e:
        print(f"代理测试失败: {e}")
        return False

# 在这里添加你的代理测试逻辑
test_proxy('http://your-proxy:port')`
    },
    {
      name: '订阅源解析模板',
      code: `# 订阅源解析脚本
import base64
import re

def parse_subscription(url):
    """解析订阅源"""
    import requests
    
    try:
        response = requests.get(url, timeout=30)
        content = base64.b64decode(response.text).decode('utf-8')
        
        # 解析节点
        nodes = []
        for line in content.split('\\n'):
            if line.strip():
                nodes.append(line.strip())
        
        print(f"解析到 {len(nodes)} 个节点")
        for i, node in enumerate(nodes[:5]):  # 显示前5个
            print(f"{i+1}: {node}")
            
        return nodes
    except Exception as e:
        print(f"解析失败: {e}")
        return []

# 在这里添加你的订阅源URL
parse_subscription('your-subscription-url')`
    },
    {
      name: '日志分析模板',
      code: `# 日志分析脚本
import json
from datetime import datetime
from pathlib import Path

def analyze_logs():
    """分析系统日志"""
    log_files = [
        'logs/collect.log',
        'logs/process.log',
        'logs/system.log'
    ]
    
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists():
            print(f"\\n分析日志文件: {log_file}")
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            error_count = len([l for l in lines if 'ERROR' in l])
            warning_count = len([l for l in lines if 'WARNING' in l])
            
            print(f"总行数: {len(lines)}")
            print(f"错误数: {error_count}")
            print(f"警告数: {warning_count}")
        else:
            print(f"日志文件不存在: {log_file}")

analyze_logs()`
    }
  ]);
  const [realTimeLogs, setRealTimeLogs] = useState<string[]>([]);
  const [wsConnections, setWsConnections] = useState<Map<string, WebSocket>>(new Map());
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const logContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchTasks();
    const interval = setInterval(fetchTasks, 5000); // 每5秒刷新一次
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // 自动滚动到日志底部
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [realTimeLogs]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const response = await tasksApi.getTasks();
      
      if (response.data.success) {
        setTasks(response.data.data || []);
      } else {
        message.error(response.data.error || '获取任务列表失败');
      }
    } catch (error) {
      console.error('获取任务失败:', error);
      message.error('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const connectWebSocket = (taskId: string) => {
    // 使用正确的后端端口
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    const wsUrl = `${protocol}//${host}:8000/api/v1/tasks/${taskId}/logs/stream`;
    
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      console.log('WebSocket连接已建立', taskId);
    };
    
    ws.onmessage = (event) => {
      const logData = JSON.parse(event.data);
      if (logData.type === 'log') {
        setRealTimeLogs(prev => [...prev, `[${logData.timestamp}] ${logData.message}`]);
      } else if (logData.type === 'status_update') {
        fetchTasks(); // 刷新任务状态
      }
    };
    
    ws.onclose = () => {
      console.log('WebSocket连接已关闭', taskId);
      setWsConnections(prev => {
        const newMap = new Map(prev);
        newMap.delete(taskId);
        return newMap;
      });
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      message.error('实时日志连接失败');
    };
    
    setWsConnections(prev => new Map(prev.set(taskId, ws)));
    return ws;
  };

  const disconnectWebSocket = (taskId: string) => {
    const ws = wsConnections.get(taskId);
    if (ws) {
      ws.close();
      setWsConnections(prev => {
        const newMap = new Map(prev);
        newMap.delete(taskId);
        return newMap;
      });
    }
  };

  const executeTask = async (taskId: string) => {
    try {
      const response = await tasksApi.executeTask(taskId);
      
      if (response.data.success) {
        message.success('任务已开始执行');
        connectWebSocket(taskId);
        fetchTasks();
      } else {
        message.error(response.data.error || '执行任务失败');
      }
    } catch (error) {
      message.error('网络错误');
    }
  };

  const cancelTask = async (taskId: string) => {
    try {
      const response = await tasksApi.cancelTask(taskId);
      
      if (response.data.success) {
        message.success('任务已取消');
        disconnectWebSocket(taskId);
        fetchTasks();
      } else {
        message.error(response.data.error || '取消任务失败');
      }
    } catch (error) {
      message.error('网络错误');
    }
  };

  const deleteTask = async (taskId: string) => {
    try {
      const response = await tasksApi.deleteTask(taskId);
      
      if (response.data.success) {
        message.success('任务已删除');
        disconnectWebSocket(taskId);
        fetchTasks();
      } else {
        message.error(response.data.error || '删除任务失败');
      }
    } catch (error) {
      message.error('网络错误');
    }
  };

  const deleteSelectedTasks = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的任务');
      return;
    }

    try {
      const promises = selectedRowKeys.map((taskId: React.Key) => 
        tasksApi.deleteTask(taskId as string)
      );
      
      const responses = await Promise.all(promises);
      
      const successCount = responses.filter(r => r.data.success).length;
      
      if (successCount === selectedRowKeys.length) {
        message.success(`成功删除 ${successCount} 个任务`);
      } else {
        message.warning(`删除了 ${successCount}/${selectedRowKeys.length} 个任务`);
      }
      
      setSelectedRowKeys([]);
      fetchTasks();
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  const quickExecute = async (values: TaskExecuteParams) => {
    try {
      let response;
      
      if (executeType === 'collect') {
        response = await tasksApi.quickCollect(values);
      } else if (executeType === 'process') {
        response = await tasksApi.quickProcess(values);
      } else if (executeType === 'custom') {
        // 创建自定义任务
        const createResponse = await tasksApi.createTask({
          task_type: 'custom',
          name: `自定义代码任务 - ${new Date().toLocaleString()}`,
          config: {
            code: customCode,
            command: customCommand,
            timeout: values.timeout || 300,
          }
        });
        
        if (createResponse.data.success && createResponse.data.data?.task_id) {
          // 立即执行创建的任务
          response = await tasksApi.executeTask(createResponse.data.data.task_id);
          // 将task_id传递给响应数据，以便后续使用
          if (response.data.success) {
            response.data.data = { task_id: createResponse.data.data.task_id };
          }
        } else {
          throw new Error(createResponse.data.error || '创建任务失败');
        }
      } else {
        throw new Error('不支持的任务类型');
      }
      
      if (response.data.success) {
        message.success('任务已开始执行');
        setQuickExecuteModalVisible(false);
        executeForm.resetFields();
        
        // 连接WebSocket监听实时日志
        if (response.data.data?.task_id) {
          connectWebSocket(response.data.data.task_id);
        }
        
        fetchTasks();
      } else {
        message.error(response.data.error || '执行失败');
      }
    } catch (error) {
      message.error('网络错误');
    }
  };

  const getStatusTag = (status: Task['status']) => {
    const statusConfig = {
      pending: { color: 'default', text: '等待中' },
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      cancelled: { color: 'warning', text: '已取消' },
    };
    
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getTaskTypeTag = (type: Task['task_type']) => {
    const typeConfig = {
      collect: { color: 'blue', text: '代理收集' },
      process: { color: 'green', text: '代理处理' },
      custom: { color: 'purple', text: '自定义' },
    };
    
    const config = typeConfig[type];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const showTaskDetail = (task: Task) => {
    setSelectedTask(task);
    setTaskDetailVisible(true);
    setRealTimeLogs(task.log_output || []);
    
    // 如果任务正在运行，连接WebSocket
    if (task.status === 'running') {
      connectWebSocket(task.id);
    }
  };

  const columns: ColumnsType<Task> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'task_type',
      key: 'task_type',
      width: 100,
      render: (type) => getTaskTypeTag(type),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress, record) => (
        <Progress
          percent={Math.round(progress)}
          size="small"
          status={record.status === 'failed' ? 'exception' : 'normal'}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time) => new Date(time).toLocaleString(),
    },
    {
      title: '耗时',
      key: 'duration',
      width: 100,
      render: (_, record) => {
        if (!record.started_at) return '-';
        
        const start = new Date(record.started_at).getTime();
        const end = record.completed_at 
          ? new Date(record.completed_at).getTime() 
          : Date.now();
        
        const duration = Math.round((end - start) / 1000);
        return `${duration}秒`;
      },
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          {record.status === 'pending' && (
            <Tooltip title="执行任务">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => executeTask(record.id)}
              />
            </Tooltip>
          )}
          {record.status === 'running' && (
            <Tooltip title="取消任务">
              <Button
                type="text"
                danger
                icon={<StopOutlined />}
                onClick={() => cancelTask(record.id)}
              />
            </Tooltip>
          )}
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => showTaskDetail(record)}
            />
          </Tooltip>
          {(record.status === 'completed' || record.status === 'failed' || record.status === 'cancelled') && (
            <Popconfirm
              title="确定要删除这个任务记录吗？"
              onConfirm={() => deleteTask(record.id)}
            >
              <Tooltip title="删除任务">
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: Task) => ({
      disabled: record.status === 'running',
    }),
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>任务监控</Title>
        <Text type="secondary">管理和监控代理爬取任务的执行状态</Text>
      </div>

      {/* SubConverter问题提示 */}
      <Alert
        message="SubConverter网络问题解决方案"
        description={
          <div>
            <p>如果任务卡在 "Updating ruleset url 'rules/LocalAreaNetwork.list'" 步骤，说明SubConverter组件在尝试更新网络规则集时遇到了连接问题。</p>
            <p><strong>解决方法</strong>：在执行任务时启用 <Tag color="orange">跳过转换</Tag> 选项，这样可以绕过SubConverter的网络依赖，让任务正常完成。</p>
          </div>
        }
        type="info"
        style={{ marginBottom: 24 }}
        showIcon
        closable
      />

      {/* 快捷操作区 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} md={8}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <RocketOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
              <div>
                <Text strong>快速执行</Text>
                <br />
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={() => setQuickExecuteModalVisible(true)}
                  style={{ marginTop: 8 }}
                >
                  启动任务
                </Button>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <BugOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
              <div>
                <Text strong>当前运行</Text>
                <br />
                <Badge
                  count={tasks.filter(t => t.status === 'running').length}
                  style={{ backgroundColor: '#52c41a' }}
                />
                <Text type="secondary" style={{ marginLeft: 8 }}>个任务</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <HistoryOutlined style={{ fontSize: 24, color: '#faad14', marginBottom: 8 }} />
              <div>
                <Text strong>今日完成</Text>
                <br />
                <Badge
                  count={tasks.filter(t => 
                    t.status === 'completed' && 
                    new Date(t.created_at).toDateString() === new Date().toDateString()
                  ).length}
                  style={{ backgroundColor: '#faad14' }}
                />
                <Text type="secondary" style={{ marginLeft: 8 }}>个任务</Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 任务列表 */}
      <Card
        title="任务列表"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchTasks}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
            <Space>
              <Text>已选择 {selectedRowKeys.length} 个任务</Text>
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 个任务吗？`}
                onConfirm={deleteSelectedTasks}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                onClick={() => setSelectedRowKeys([])}
              >
                取消选择
              </Button>
            </Space>
          </div>
        )}

        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          rowSelection={rowSelection}
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 快速执行模态框 */}
      <Modal
        title="快速执行任务"
        open={quickExecuteModalVisible}
        onCancel={() => {
          setQuickExecuteModalVisible(false);
          executeForm.resetFields();
        }}
        onOk={() => executeForm.submit()}
        width={600}
        destroyOnHidden
      >
        <Alert
          message="网络连接提示"
          description="如果遇到SubConverter卡住不动的问题，通常是网络连接或规则集更新问题。建议启用'跳过转换'选项来绕过SubConverter组件。"
          type="info"
          style={{ marginBottom: 16 }}
          showIcon
        />
        
        <Form
          form={executeForm}
          layout="vertical"
          onFinish={quickExecute}
          initialValues={{
            all_config: true,
            overwrite: false,
            skip_check: true,
            skip_subconverter: false,
            num_threads: 64,
            delay: 5000,
            timeout: 5000,
            retry: 3,
          }}
        >
          <Form.Item label="任务类型">
            <Select
              value={executeType}
              onChange={setExecuteType}
              style={{ width: '100%' }}
            >
              <Option value="collect">代理收集 (collect.py)</Option>
              <Option value="process">代理处理 (process.py)</Option>
              <Option value="custom">自定义代码执行</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="num_threads" label="线程数量">
                <InputNumber min={1} max={200} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="delay" label="最大延迟(ms)">
                <InputNumber min={1000} max={10000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="overwrite" valuePropName="checked" label="覆盖模式">
                <Switch checkedChildren="覆盖现有" unCheckedChildren="保留现有" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="skip_check" valuePropName="checked" label="节点验证">
                <Switch checkedChildren="跳过检查" unCheckedChildren="检查节点" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="skip_subconverter" valuePropName="checked" label="SubConverter">
                <Switch checkedChildren="跳过转换" unCheckedChildren="启用转换" />
              </Form.Item>
            </Col>
          </Row>

          {executeType === 'collect' && (
            <>
              <Form.Item name="gist" label="GitHub Gist">
                <Input placeholder="username/gist_id" />
              </Form.Item>
              <Form.Item name="key" label="GitHub Token">
                <Input.Password placeholder="个人访问令牌" />
              </Form.Item>
            </>
          )}

          {executeType === 'process' && (
            <>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="timeout" label="超时时间(ms)">
                    <InputNumber min={1000} max={30000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="retry" label="重试次数">
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item name="server_config" label="远程配置">
                <Input placeholder="远程配置文件URL（可选）" />
              </Form.Item>
            </>
          )}

          {executeType === 'custom' && (
            <>
              <Tabs defaultActiveKey="code">
                <Tabs.TabPane tab={<span><CodeOutlined />Python代码</span>} key="code">
                  <Row style={{ marginBottom: 16 }}>
                    <Col span={24}>
                      <Text strong>代码模板:</Text>
                      <Space wrap style={{ marginLeft: 8 }}>
                        {codeTemplates.map((template, index) => (
                          <Button
                            key={index}
                            size="small"
                            onClick={() => setCustomCode(template.code)}
                          >
                            {template.name}
                          </Button>
                        ))}
                        <Button
                          size="small"
                          onClick={() => setCustomCode('# Python代码示例\nprint("Hello, World!")')}
                        >
                          清空
                        </Button>
                      </Space>
                    </Col>
                  </Row>
                  <div style={{ border: '1px solid #d9d9d9', borderRadius: 6 }}>
                    <MonacoEditor
                      width="100%"
                      height="300"
                      language="python"
                      theme="vs"
                      value={customCode}
                      onChange={setCustomCode}
                      options={{
                        selectOnLineNumbers: true,
                        automaticLayout: true,
                        minimap: { enabled: false },
                        fontSize: 14,
                        lineNumbers: 'on',
                        roundedSelection: false,
                        scrollBeyondLastLine: false,
                        readOnly: false,
                        theme: 'vs'
                      }}
                    />
                  </div>
                </Tabs.TabPane>
                <Tabs.TabPane tab={<span><ThunderboltOutlined />命令行</span>} key="command">
                  <Form.Item label="自定义命令">
                    <Input.TextArea
                      rows={4}
                      placeholder="输入要执行的命令，例如：python your_script.py"
                      value={customCommand}
                      onChange={(e) => setCustomCommand(e.target.value)}
                    />
                  </Form.Item>
                  <Alert
                    message="命令执行说明"
                    description="可以执行任意Shell命令。注意：命令将在项目根目录下执行，请确保命令的安全性。"
                    type="warning"
                    showIcon
                    style={{ marginTop: 8 }}
                  />
                </Tabs.TabPane>
              </Tabs>
              
              <Divider />
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="timeout" label="执行超时(秒)">
                    <InputNumber 
                      min={30} 
                      max={3600} 
                      defaultValue={300}
                      style={{ width: '100%' }} 
                      placeholder="默认300秒"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <div style={{ paddingTop: 30 }}>
                    <Alert
                      message="安全提示"
                      description="自定义代码在受限环境中执行，某些系统操作可能被限制。"
                      type="info"
                      showIcon
                    />
                  </div>
                </Col>
              </Row>
            </>
          )}
        </Form>
      </Modal>

      {/* 任务详情抽屉 */}
      <Drawer
        title="任务详情"
        placement="right"
        size="large"
        open={taskDetailVisible}
        onClose={() => {
          setTaskDetailVisible(false);
          setSelectedTask(null);
          setRealTimeLogs([]);
          // 关闭WebSocket连接
          if (selectedTask) {
            disconnectWebSocket(selectedTask.id);
          }
        }}
      >
        {selectedTask && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>任务名称：</Text>
                  <Text>{selectedTask.name}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>任务类型：</Text>
                  {getTaskTypeTag(selectedTask.task_type)}
                </Col>
              </Row>
              <Row gutter={16} style={{ marginTop: 8 }}>
                <Col span={12}>
                  <Text strong>状态：</Text>
                  {getStatusTag(selectedTask.status)}
                </Col>
                <Col span={12}>
                  <Text strong>进度：</Text>
                  <Progress percent={Math.round(selectedTask.progress)} size="small" />
                </Col>
              </Row>
              {selectedTask.error_message && (
                <Alert
                  message="错误信息"
                  description={selectedTask.error_message}
                  type="error"
                  style={{ marginTop: 16 }}
                />
              )}
            </Card>

            <Card
              title={
                <Space>
                  <FileTextOutlined />
                  <span>实时日志</span>
                  {selectedTask.status === 'running' && (
                    <Badge status="processing" text="运行中" />
                  )}
                </Space>
              }
              size="small"
            >
              <div
                ref={logContainerRef}
                style={{
                  height: 400,
                  overflow: 'auto',
                  backgroundColor: '#001529',
                  color: '#fff',
                  padding: 16,
                  fontFamily: 'monospace',
                  fontSize: 12,
                  whiteSpace: 'pre-wrap',
                }}
              >
                {realTimeLogs.length > 0 ? (
                  realTimeLogs.map((log, index) => (
                    <div key={index}>{log}</div>
                  ))
                ) : (
                  <Text type="secondary">暂无日志输出</Text>
                )}
              </div>
            </Card>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default TaskMonitoring; 