# Aggregator Web 界面

这是 Aggregator 代理池聚合器的 Web 界面，提供了友好的图形用户界面来管理和使用 Aggregator 的所有功能。

## 功能特性

- 📊 **仪表板**：系统概览、节点统计和活动监控
- 🌐 **节点管理**：查看、测试、筛选和导出代理节点
- 🔄 **订阅管理**：管理订阅源、刷新订阅和查看状态
- 🛠️ **转换工具**：将订阅转换为不同客户端格式
- ⚙️ **配置管理**：修改系统配置和环境变量
- 📝 **日志查看**：查看系统日志和错误信息

## 技术栈

- **前端**：React + TypeScript + Ant Design
- **后端**：FastAPI + Python
- **部署**：Docker + Docker Compose

## 快速开始

### 方法一：使用 Docker Compose（推荐）

1. 确保已安装 Docker 和 Docker Compose
2. 克隆仓库：`git clone https://github.com/wzdnzd/aggregator.git`
3. 进入目录：`cd aggregator/web`
4. 启动服务：`docker-compose up -d`
5. 访问：`http://localhost:8000`

### 方法二：手动启动

#### 前提条件

- Node.js 16+ 和 npm
- Python 3.7+
- pip

#### 步骤

1. 克隆仓库：`git clone https://github.com/wzdnzd/aggregator.git`
2. 进入目录：`cd aggregator/web`

**Windows:**
```
.\start.bat
```

**Linux/macOS:**
```
chmod +x start.sh
./start.sh
```

3. 访问：`http://localhost:8000`

## 开发指南

### 前端开发

```bash
cd web
npm install --legacy-peer-deps
npm start
```

前端开发服务器将在 `http://localhost:3000` 启动。

### 后端开发

```bash
cd web/backend
pip install -r requirements.txt
python main.py
```

后端API将在 `http://localhost:8000` 启动。

## 项目结构

```
web/
├── src/                 # 前端React代码
│   ├── components/      # 共享组件
│   ├── pages/           # 页面组件
│   ├── services/        # API服务
│   └── types/           # TypeScript类型定义
├── backend/             # 后端FastAPI代码
│   ├── app/             # 应用代码
│   │   ├── api/         # API路由
│   │   ├── core/        # 核心功能
│   │   └── models/      # 数据模型
│   └── main.py          # 入口文件
├── docker-compose.yml   # Docker Compose配置
├── start.sh             # Linux/macOS启动脚本
└── start.bat            # Windows启动脚本
```

## 配置

系统配置可以通过以下方式设置：

1. 环境变量
2. `.env` 文件
3. Web界面的系统设置页面

主要配置项：

- `GIST_PAT`：GitHub Gist 个人访问令牌
- `GIST_LINK`：GitHub Gist 链接
- `CUSTOMIZE_LINK`：自定义机场链接
- `SKIP_ALIVE_CHECK`：是否跳过节点可用性检查
- `WORKFLOW_MODE`：工作流模式
- `ENABLE_SPECIAL_PROTOCOLS`：是否启用特殊协议

## 常见问题

**Q: 启动时报错 "Module not found: Error: Can't resolve './App'"**

A: 这通常是由于TypeScript版本不兼容导致的。尝试使用 `--legacy-peer-deps` 标志安装依赖：
```
npm install --legacy-peer-deps
```

**Q: 后端无法连接到Aggregator核心模块**

A: 确保Aggregator核心模块已正确安装，并且后端服务可以访问到它。如果使用Docker，确保卷映射正确。

## 贡献

欢迎提交问题和拉取请求！

## 许可证

与Aggregator主项目相同的许可证。 