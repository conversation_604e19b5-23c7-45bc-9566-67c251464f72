@echo off
echo ====================================
echo    快速启动 Aggregator Web 界面
echo ====================================

REM 设置Node.js内存限制
set NODE_OPTIONS=--max-old-space-size=4096

REM 设置优化环境变量
set GENERATE_SOURCEMAP=false
set DISABLE_ESLINT_PLUGIN=true
set TSC_COMPILE_ON_ERROR=true
set FAST_REFRESH=true
set BROWSER=none

echo 正在启动开发服务器（优化模式）...
echo 提示：首次启动可能需要1-2分钟，后续启动会更快
echo.

REM 启动开发服务器
npm run start:fast

pause
