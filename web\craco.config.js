// CRACO配置文件 - 用于自定义Create React App的webpack配置
// 如果需要更深度的优化，可以安装 @craco/craco 并使用此配置

module.exports = {
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      // 开发环境优化
      if (env === 'development') {
        // 禁用source map以提升编译速度
        webpackConfig.devtool = false;
        
        // 优化模块解析
        webpackConfig.resolve.symlinks = false;
        
        // 缓存配置
        webpackConfig.cache = {
          type: 'filesystem',
          buildDependencies: {
            config: [__filename]
          }
        };
        
        // 优化分包
        webpackConfig.optimization = {
          ...webpackConfig.optimization,
          splitChunks: {
            chunks: 'all',
            cacheGroups: {
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendors',
                chunks: 'all',
              },
              monaco: {
                test: /[\\/]node_modules[\\/]monaco-editor[\\/]/,
                name: 'monaco',
                chunks: 'all',
              }
            }
          }
        };
      }
      
      return webpackConfig;
    }
  },
  devServer: {
    // 开发服务器优化
    compress: true,
    hot: true,
    liveReload: false, // 禁用live reload，只使用HMR
  }
};
