# 依赖
/node_modules
/.pnp
.pnp.js

# 测试
/coverage

# 生产构建
/build

# 杂项
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# 数据文件
/data/

# 日志
/logs/
*.log

# IDE
.idea/
.vscode/
*.swp
*.swo 