import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Space,
  Tag,
  Popconfirm,
  message,
  Modal,
  Form,
  Select,
  Row,
  Col,
  Typography,
  Progress,
  Tooltip,
  Badge,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EditOutlined,
  LinkOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  EyeOutlined,
} from '@ant-design/icons';

const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface Subscription {
  id: string;
  name: string;
  url: string;
  type: 'airport' | 'free' | 'custom';
  status: 'active' | 'inactive' | 'expired' | 'error';
  node_count: number;
  last_update: string;
  traffic_used: number;
  traffic_total: number;
  expire_date?: string;
  auto_renew: boolean;
  tags: string[];
}

const SubscriptionManagement: React.FC = () => {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSubscription, setEditingSubscription] = useState<Subscription | null>(null);
  const [form] = Form.useForm();

  // 模拟数据
  const mockData: Subscription[] = [
    {
      id: '1',
      name: '高速机场A',
      url: 'https://example.com/subscribe',
      type: 'airport',
      status: 'active',
      node_count: 156,
      last_update: '2024-01-21 14:20:00',
      traffic_used: 25600,
      traffic_total: 102400,
      expire_date: '2024-02-15',
      auto_renew: true,
      tags: ['高速', '稳定'],
    },
    {
      id: '2',
      name: '免费节点B',
      url: 'https://example2.com/subscribe',
      type: 'free',
      status: 'active',
      node_count: 45,
      last_update: '2024-01-21 13:45:00',
      traffic_used: 0,
      traffic_total: 0,
      expire_date: undefined,
      auto_renew: false,
      tags: ['免费'],
    },
    {
      id: '3',
      name: '试用机场C',
      url: 'https://example3.com/subscribe',
      type: 'airport',
      status: 'expired',
      node_count: 89,
      last_update: '2024-01-20 16:30:00',
      traffic_used: 5120,
      traffic_total: 10240,
      expire_date: '2024-01-20',
      auto_renew: false,
      tags: ['试用', '过期'],
    },
  ];

  useEffect(() => {
    fetchSubscriptions();
  }, []);

  const fetchSubscriptions = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setSubscriptions(mockData);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('获取订阅列表失败');
      setLoading(false);
    }
  };

  const handleRefreshSubscription = async (subscriptionId: string) => {
    try {
      message.loading({ content: '正在刷新订阅...', key: 'refresh' });
      // 模拟API调用
      setTimeout(() => {
        message.success({ content: '订阅刷新成功', key: 'refresh' });
        fetchSubscriptions();
      }, 2000);
    } catch (error) {
      message.error({ content: '订阅刷新失败', key: 'refresh' });
    }
  };

  const handleDeleteSubscription = async (subscriptionId: string) => {
    try {
      setSubscriptions(prev => prev.filter(sub => sub.id !== subscriptionId));
      message.success('订阅删除成功');
    } catch (error) {
      message.error('订阅删除失败');
    }
  };

  const handleEditSubscription = (subscription: Subscription) => {
    setEditingSubscription(subscription);
    form.setFieldsValue(subscription);
    setModalVisible(true);
  };

  const handleAddSubscription = () => {
    setEditingSubscription(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingSubscription) {
        // 更新订阅
        setSubscriptions(prev =>
          prev.map(sub =>
            sub.id === editingSubscription.id
              ? { ...sub, ...values }
              : sub
          )
        );
        message.success('订阅更新成功');
      } else {
        // 添加新订阅
        const newSubscription: Subscription = {
          id: Date.now().toString(),
          ...values,
          node_count: 0,
          last_update: new Date().toLocaleString(),
          status: 'active' as const,
          traffic_used: 0,
          traffic_total: 0,
        };
        setSubscriptions(prev => [...prev, newSubscription]);
        message.success('订阅添加成功');
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'expired': return 'error';
      case 'error': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '正常';
      case 'inactive': return '停用';
      case 'expired': return '过期';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'airport': return 'blue';
      case 'free': return 'green';
      case 'custom': return 'purple';
      default: return 'default';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'airport': return '机场';
      case 'free': return '免费';
      case 'custom': return '自定义';
      default: return '未知';
    }
  };

  const columns = [
    {
      title: '订阅名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
      render: (text: string, record: Subscription) => (
        <Space>
          <LinkOutlined />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>{getTypeText(type)}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Badge
          status={getStatusColor(status) as any}
          text={getStatusText(status)}
        />
      ),
    },
    {
      title: '节点数',
      dataIndex: 'node_count',
      key: 'node_count',
      width: 100,
      sorter: (a: Subscription, b: Subscription) => a.node_count - b.node_count,
      render: (count: number) => (
        <Text type={count > 0 ? 'success' : 'secondary'}>{count}</Text>
      ),
    },
    {
      title: '流量使用',
      key: 'traffic',
      width: 150,
      render: (_: any, record: Subscription) => {
        if (record.traffic_total === 0) {
          return <Text type="secondary">无限制</Text>;
        }
        const percent = Math.round((record.traffic_used / record.traffic_total) * 100);
        return (
          <div>
            <Progress
              percent={percent}
              size="small"
              status={percent > 80 ? 'exception' : 'normal'}
            />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {(record.traffic_used / 1024).toFixed(1)}GB / {(record.traffic_total / 1024).toFixed(1)}GB
            </Text>
          </div>
        );
      },
    },
    {
      title: '过期时间',
      dataIndex: 'expire_date',
      key: 'expire_date',
      width: 120,
      render: (date: string) => {
        if (!date) return <Text type="secondary">永久</Text>;
        const isExpired = new Date(date) < new Date();
        return (
          <Text type={isExpired ? 'danger' : 'secondary'}>
            {date}
          </Text>
        );
      },
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 120,
      render: (tags: string[]) => (
        <div>
          {tags.map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </div>
      ),
    },
    {
      title: '最后更新',
      dataIndex: 'last_update',
      key: 'last_update',
      width: 150,
      render: (time: string) => (
        <Text type="secondary">{time}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: Subscription) => (
        <Space>
          <Tooltip title="刷新订阅">
            <Button
              type="text"
              icon={<SyncOutlined />}
              onClick={() => handleRefreshSubscription(record.id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditSubscription(record)}
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => message.info('查看详情功能开发中')}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个订阅吗？"
            onConfirm={() => handleDeleteSubscription(record.id)}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={3}>订阅管理</Title>
          <Text type="secondary">管理和监控所有代理订阅源</Text>
        </div>

        {/* 工具栏 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddSubscription}
              >
                添加订阅
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchSubscriptions}
                loading={loading}
              >
                刷新
              </Button>
              {selectedRowKeys.length > 0 && (
                <>
                  <Button
                    icon={<SyncOutlined />}
                    onClick={() => message.info('批量刷新功能开发中')}
                  >
                    批量刷新
                  </Button>
                  <Popconfirm
                    title={`确定要删除选中的 ${selectedRowKeys.length} 个订阅吗？`}
                    onConfirm={() => message.info('批量删除功能开发中')}
                  >
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                    >
                      批量删除
                    </Button>
                  </Popconfirm>
                </>
              )}
            </Space>
          </Col>
        </Row>

        {/* 订阅列表 */}
        <Table
          columns={columns}
          dataSource={subscriptions}
          rowKey="id"
          rowSelection={rowSelection}
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 添加/编辑订阅模态框 */}
      <Modal
        title={editingSubscription ? '编辑订阅' : '添加订阅'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'airport',
            auto_renew: false,
            tags: [],
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="订阅名称"
                rules={[{ required: true, message: '请输入订阅名称' }]}
              >
                <Input placeholder="输入订阅名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="订阅类型"
                rules={[{ required: true, message: '请选择订阅类型' }]}
              >
                <Select>
                  <Option value="airport">机场</Option>
                  <Option value="free">免费</Option>
                  <Option value="custom">自定义</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="url"
            label="订阅链接"
            rules={[
              { required: true, message: '请输入订阅链接' },
              { type: 'url', message: '请输入有效的URL' },
            ]}
          >
            <TextArea
              rows={3}
              placeholder="输入订阅链接"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="traffic_total"
                label="总流量 (MB)"
              >
                <Input
                  type="number"
                  placeholder="0 表示无限制"
                  addonAfter="MB"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="expire_date"
                label="过期时间"
              >
                <Input
                  type="date"
                  placeholder="留空表示永久"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="添加标签"
              tokenSeparators={[',']}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SubscriptionManagement;