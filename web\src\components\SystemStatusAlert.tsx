import React from 'react';
import { Al<PERSON>, Space, Typography, Button } from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  StopOutlined
} from '@ant-design/icons';

const { Text } = Typography;

export interface SystemStatusInfo {
  type: 'success' | 'info' | 'warning' | 'error';
  icon: React.ReactNode;
  message: string;
  description: string;
  color: string;
}

export interface SystemStatusAlertProps {
  tasks?: any[];
  showActions?: boolean;
  onQuickCollect?: () => void;
  onQuickProcess?: () => void;
  collectLoading?: boolean;
  processLoading?: boolean;
  size?: 'small' | 'default' | 'large';
  customMessage?: string;
  customDescription?: string;
}

// 获取系统状态信息的工具函数
export const getSystemStatusInfo = (tasks: any[] = []): SystemStatusInfo => {
  const runningTasks = tasks.filter(t => t.status === 'running').length;
  const failedTasks = tasks.filter(t => t.status === 'failed').length;
  const cancelledTasks = tasks.filter(t => t.status === 'cancelled').length;
  
  // 如果有失败的任务
  if (failedTasks > 0) {
    return {
      type: 'error',
      icon: <WarningOutlined style={{ color: '#ff4d4f' }} />,
      message: '系统运行异常',
      description: `检测到 ${failedTasks} 个失败任务，建议检查系统状态和日志`,
      color: '#ff4d4f'
    };
  }
  
  // 如果有取消的任务
  if (cancelledTasks > 0 && runningTasks === 0) {
    return {
      type: 'warning',
      icon: <StopOutlined style={{ color: '#faad14' }} />,
      message: '系统部分异常',
      description: `检测到 ${cancelledTasks} 个已取消任务，系统功能可能受影响`,
      color: '#faad14'
    };
  }
  
  // 如果有正在运行的任务
  if (runningTasks > 0) {
    return {
      type: 'info',
      icon: <ClockCircleOutlined style={{ color: '#1890ff' }} />,
      message: '系统运行中',
      description: `当前有 ${runningTasks} 个任务正在执行，所有核心服务正常运行`,
      color: '#1890ff'
    };
  }
  
  // 默认正常状态
  return {
    type: 'success',
    icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
    message: '系统运行正常',
    description: '所有核心服务正常运行，点击下方按钮可快速执行原项目功能',
    color: '#52c41a'
  };
};

const SystemStatusAlert: React.FC<SystemStatusAlertProps> = ({
  tasks = [],
  showActions = true,
  onQuickCollect,
  onQuickProcess,
  collectLoading = false,
  processLoading = false,
  size = 'default',
  customMessage,
  customDescription
}) => {
  const statusInfo = getSystemStatusInfo(tasks);
  
  // 如果有自定义消息，使用自定义消息
  const finalMessage = customMessage || statusInfo.message;
  const finalDescription = customDescription || statusInfo.description;
  
  const getBorderColor = () => {
    switch (statusInfo.type) {
      case 'success': return '#b7eb8f';
      case 'warning': return '#ffe58f';
      case 'error': return '#ffccc7';
      case 'info': return '#91d5ff';
      default: return '#b7eb8f';
    }
  };
  
  const getBackgroundColor = () => {
    switch (statusInfo.type) {
      case 'success': return '#f6ffed';
      case 'warning': return '#fffbe6';
      case 'error': return '#fff2f0';
      case 'info': return '#e6f7ff';
      default: return '#f6ffed';
    }
  };
  
  return (
    <Alert
      message={
        <Space>
          {statusInfo.icon}
          <Text strong style={{ fontSize: size === 'small' ? '12px' : '14px' }}>
            {finalMessage}
          </Text>
        </Space>
      }
      description={finalDescription}
      type={statusInfo.type}
      showIcon={false}
      style={{
        border: `1px solid ${getBorderColor()}`,
        backgroundColor: getBackgroundColor(),
        fontSize: size === 'small' ? '12px' : '14px',
        padding: size === 'small' ? '8px 12px' : '16px'
      }}
      action={
        showActions && onQuickCollect && onQuickProcess ? (
          <Space>
            <Button
              size={size === 'small' ? 'small' : 'middle'}
              type="primary"
              ghost
              onClick={onQuickCollect}
              loading={collectLoading}
            >
              快速收集
            </Button>
            <Button
              size={size === 'small' ? 'small' : 'middle'}
              type="primary"
              ghost
              onClick={onQuickProcess}
              loading={processLoading}
            >
              快速处理
            </Button>
          </Space>
        ) : undefined
      }
    />
  );
};

export default SystemStatusAlert;
