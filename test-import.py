#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本：验证 SubConverter 模块移除后的导入是否正常
"""

import sys
import os

# 添加 subscribe 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'subscribe'))

def test_imports():
    """测试所有模块的导入"""
    print("🔍 测试模块导入...")
    
    try:
        print("  ✓ 测试 executable 模块...")
        import executable
        clash_bin = executable.which_bin()
        print(f"    Clash 二进制文件: {clash_bin}")
        
        print("  ✓ 测试 airport 模块...")
        import airport
        print("    Airport 模块导入成功")
        
        print("  ✓ 测试 crawl 模块...")
        import crawl
        print("    Crawl 模块导入成功")
        
        print("  ✓ 测试 collect 模块...")
        import collect
        print("    Collect 模块导入成功")
        
        print("  ✓ 测试 process 模块...")
        import process
        print("    Process 模块导入成功")
        
        print("  ✓ 测试 v2rayse 脚本...")
        from scripts import v2rayse
        print("    V2RaySE 脚本导入成功")
        
        print("\n✅ 所有模块导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 其他错误: {e}")
        return False

def test_subconverter_removal():
    """测试 SubConverter 相关功能是否已正确移除"""
    print("\n🗑️ 测试 SubConverter 移除...")
    
    # 检查 subconverter 目录是否已删除
    subconverter_dir = os.path.join(os.path.dirname(__file__), 'subconverter')
    if os.path.exists(subconverter_dir):
        print("  ❌ subconverter 目录仍然存在")
        return False
    else:
        print("  ✓ subconverter 目录已删除")
    
    # 检查 subconverter.py 文件是否已删除
    subconverter_py = os.path.join(os.path.dirname(__file__), 'subscribe', 'subconverter.py')
    if os.path.exists(subconverter_py):
        print("  ❌ subscribe/subconverter.py 文件仍然存在")
        return False
    else:
        print("  ✓ subscribe/subconverter.py 文件已删除")
    
    print("\n✅ SubConverter 移除测试通过！")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("  SubConverter 移除后的导入测试")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    # 测试移除
    removal_success = test_subconverter_removal()
    
    print("\n" + "=" * 50)
    if import_success and removal_success:
        print("🎉 所有测试通过！SubConverter 已成功移除，其他模块正常工作。")
        return 0
    else:
        print("💥 测试失败！请检查上述错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
