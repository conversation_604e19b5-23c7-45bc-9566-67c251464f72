import React, { Component, ReactNode } from 'react';
import { Result, Button } from 'antd';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

interface ErrorInfo {
  componentStack: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级 UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 你同样可以将错误日志上报给服务器
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // 你可以自定义降级 UI 并渲染
      return (
        <Result
          status="error"
          title="页面加载失败"
          subTitle={this.state.error?.message || '发生了未知错误'}
          extra={[
            <Button 
              type="primary" 
              key="reload" 
              onClick={() => window.location.reload()}
            >
              刷新页面
            </Button>,
            <Button 
              key="home" 
              onClick={() => window.location.href = '/'}
            >
              返回首页
            </Button>
          ]}
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 