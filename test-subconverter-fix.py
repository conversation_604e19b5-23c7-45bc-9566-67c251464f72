#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 SubConverter 相关错误修复
"""

import sys
import os

# 添加 subscribe 目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'subscribe'))

def test_collect_import():
    """测试 collect.py 导入"""
    try:
        import collect
        print("✅ collect.py 导入成功")
        
        # 测试 CONVERT_TARGETS 常量
        if hasattr(collect, 'CONVERT_TARGETS'):
            print(f"✅ CONVERT_TARGETS 定义成功: {len(collect.CONVERT_TARGETS)} 个格式")
        else:
            print("❌ CONVERT_TARGETS 未定义")
            return False
            
        # 测试 get_filename 函数
        if hasattr(collect, 'get_filename'):
            filename = collect.get_filename('clash')
            print(f"✅ get_filename 函数正常: clash -> {filename}")
        else:
            print("❌ get_filename 函数未定义")
            return False
            
        return True
    except Exception as e:
        print(f"❌ collect.py 导入失败: {e}")
        return False

def test_process_import():
    """测试 process.py 导入"""
    try:
        import process
        print("✅ process.py 导入成功")
        
        # 测试 CONVERT_TARGETS 常量
        if hasattr(process, 'CONVERT_TARGETS'):
            print(f"✅ CONVERT_TARGETS 定义成功: {len(process.CONVERT_TARGETS)} 个格式")
        else:
            print("❌ CONVERT_TARGETS 未定义")
            return False
            
        # 测试 get_filename 函数
        if hasattr(process, 'get_filename'):
            filename = process.get_filename('v2ray')
            print(f"✅ get_filename 函数正常: v2ray -> {filename}")
        else:
            print("❌ get_filename 函数未定义")
            return False
            
        return True
    except Exception as e:
        print(f"❌ process.py 导入失败: {e}")
        return False

def test_airport_import():
    """测试 airport.py 导入"""
    try:
        import airport
        print("✅ airport.py 导入成功")
        return True
    except Exception as e:
        print(f"❌ airport.py 导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("  SubConverter 错误修复验证")
    print("=" * 50)
    
    tests = [
        ("collect.py 导入测试", test_collect_import),
        ("process.py 导入测试", test_process_import),
        ("airport.py 导入测试", test_airport_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"💥 {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有 SubConverter 相关错误已修复！")
        return 0
    else:
        print("💥 仍有错误需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
