#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from typing import List, Union

# 在Pydantic v2.x中，BaseSettings已移至pydantic-settings包
try:
    # 尝试从新位置导入 (Pydantic v2)
    from pydantic_settings import BaseSettings, SettingsConfigDict
    from pydantic import field_validator
    HAS_PYDANTIC_V2 = True
except ImportError:
    try:
        # 尝试从Pydantic v1导入
        from pydantic import BaseSettings, validator
        field_validator = validator
        HAS_PYDANTIC_V2 = False
    except ImportError as e:
        # 如果都失败了，给出明确的错误信息
        raise ImportError(
            "无法导入Pydantic设置。请安装 'pip install pydantic-settings' (Pydantic v2) "
            "或确保安装了正确版本的Pydantic。"
        ) from e

class Settings(BaseSettings):
    """应用配置"""
    
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Aggregator Web"
    DEBUG: bool = False
    
    # CORS设置
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # 数据库设置
    DATABASE_URL: str = "sqlite:///./aggregator.db"
    
    # JWT设置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # 项目路径
    PROJECT_ROOT: str = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    
    # 使用适当的验证器装饰器
    @field_validator("BACKEND_CORS_ORIGINS", mode='before')
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # 根据Pydantic版本使用不同的配置方式
    if HAS_PYDANTIC_V2:
        # Pydantic v2.x 配置
        model_config = SettingsConfigDict(
            case_sensitive=True,
            env_file=".env"
        )
    else:
        # Pydantic v1.x 配置
        class Config:
            case_sensitive = True
            env_file = ".env"

settings = Settings() 