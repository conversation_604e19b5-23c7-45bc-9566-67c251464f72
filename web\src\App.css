body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ant-layout {
  min-height: 100vh;
}

.site-layout-content {
  background: #fff;
  padding: 24px;
  min-height: 280px;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
}

.ant-row-rtl .logo {
  float: right;
  margin: 16px 0 16px 24px;
}

.ant-layout-header {
  display: flex;
  align-items: center;
}

.ant-layout-header h1 {
  color: #fff;
  margin: 0 0 0 12px;
}

.site-layout-background {
  background: #fff;
}

.app-container {
  min-height: 100vh;
}

.content-container {
  padding: 24px;
  min-height: 280px;
}

.header-controls {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.header-controls > * {
  margin-left: 12px;
}

.ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.stats-card {
  margin-bottom: 24px;
}

.stats-card .ant-statistic-title {
  font-size: 14px;
}

.chart-card {
  margin-bottom: 24px;
}

.chart-card .ant-card-head {
  border-bottom: 0;
}

.table-card {
  margin-bottom: 24px;
}

.table-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.table-search {
  width: 300px;
}

.node-status-badge {
  margin-right: 8px;
}

.node-tag {
  margin: 0 4px 4px 0;
}

.filter-dropdown {
  padding: 8px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.filter-dropdown input {
  width: 188px;
  margin-bottom: 8px;
  display: block;
}

.filter-dropdown button {
  width: 90px;
}

.filter-dropdown button:first-child {
  margin-right: 8px;
}

/* 暗色模式适配 */
.dark-theme .site-layout-content {
  background: #141414;
}

.dark-theme .site-layout-background {
  background: #141414;
}

.dark-theme .ant-card {
  background: #1f1f1f;
}

.dark-theme .ant-table {
  background: #1f1f1f;
}

.dark-theme .ant-table-thead > tr > th {
  background: #1f1f1f;
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-table-tbody > tr > td {
  border-bottom: 1px solid #303030;
}

.dark-theme .ant-table-tbody > tr.ant-table-row:hover > td {
  background: #303030;
}

.dark-theme .ant-input {
  background-color: #141414;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-input:hover {
  border-color: #165996;
}

.dark-theme .ant-input:focus,
.dark-theme .ant-input-focused {
  border-color: #177ddc;
  box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
}

.dark-theme .ant-input-affix-wrapper {
  background-color: #141414;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-input-affix-wrapper:hover {
  border-color: #165996;
}

.dark-theme .ant-input-affix-wrapper:focus,
.dark-theme .ant-input-affix-wrapper-focused {
  border-color: #177ddc;
  box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
}

.dark-theme .ant-select-selector {
  background-color: #141414 !important;
  border-color: #434343 !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark-theme .ant-select-arrow {
  color: rgba(255, 255, 255, 0.3);
}

.dark-theme .ant-select-dropdown {
  background-color: #1f1f1f;
}

.dark-theme .ant-select-item {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: #111b26;
}

.dark-theme .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #111b26;
}

.dark-theme .ant-pagination-item a {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-pagination-item-active {
  background-color: #177ddc;
  border-color: #177ddc;
}

.dark-theme .ant-pagination-item-active a {
  color: #fff;
}

.dark-theme .ant-pagination-prev .ant-pagination-item-link,
.dark-theme .ant-pagination-next .ant-pagination-item-link {
  background-color: transparent;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-pagination-disabled .ant-pagination-item-link {
  color: rgba(255, 255, 255, 0.3);
  border-color: #434343;
}

.dark-theme .ant-btn {
  background: #141414;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-btn-primary {
  background: #177ddc;
  border-color: #177ddc;
  color: #fff;
}

.dark-theme .ant-btn-dangerous {
  background: #a61d24;
  border-color: #a61d24;
  color: #fff;
}

.dark-theme .ant-modal-content {
  background-color: #1f1f1f;
}

.dark-theme .ant-modal-header {
  background-color: #1f1f1f;
  border-bottom: 1px solid #303030;
}

.dark-theme .ant-modal-title {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-modal-close {
  color: rgba(255, 255, 255, 0.45);
}

.dark-theme .ant-modal-footer {
  border-top: 1px solid #303030;
}

.dark-theme .ant-drawer-content {
  background-color: #1f1f1f;
}

.dark-theme .ant-drawer-header {
  background-color: #1f1f1f;
  border-bottom: 1px solid #303030;
}

.dark-theme .ant-drawer-title {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-drawer-close {
  color: rgba(255, 255, 255, 0.45);
}

.dark-theme .ant-descriptions-item-label {
  background-color: rgba(255, 255, 255, 0.04);
}

.dark-theme .ant-descriptions-bordered .ant-descriptions-item-label {
  border-right: 1px solid #303030;
}

.dark-theme .ant-descriptions-bordered .ant-descriptions-item-content {
  border-right: 1px solid #303030;
}

.dark-theme .ant-descriptions-bordered .ant-descriptions-row {
  border-bottom: 1px solid #303030;
}

.dark-theme .ant-collapse {
  background-color: #1f1f1f;
  border-color: #303030;
}

.dark-theme .ant-collapse-header {
  color: rgba(255, 255, 255, 0.85) !important;
}

.dark-theme .ant-collapse-content {
  background-color: #141414;
  border-top-color: #303030;
}

.dark-theme .ant-collapse-item {
  border-bottom-color: #303030;
}

.dark-theme .ant-tabs-tab {
  color: rgba(255, 255, 255, 0.45);
}

.dark-theme .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #177ddc;
}

.dark-theme .ant-tabs-ink-bar {
  background: #177ddc;
}

.dark-theme .ant-tabs-nav::before {
  border-bottom-color: #303030;
}

.dark-theme .ant-tabs-content-holder {
  background-color: #141414;
}

.dark-theme .ant-progress-inner {
  background-color: rgba(255, 255, 255, 0.08);
}

.dark-theme .ant-alert {
  background-color: #1f1f1f;
  border-color: #303030;
}

.dark-theme .ant-alert-message {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-alert-description {
  color: rgba(255, 255, 255, 0.45);
}

.dark-theme .ant-list {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-list-item {
  border-bottom-color: #303030;
}

.dark-theme .ant-list-item-meta-title {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-list-item-meta-description {
  color: rgba(255, 255, 255, 0.45);
}

.dark-theme .ant-empty-description {
  color: rgba(255, 255, 255, 0.45);
}

.dark-theme .ant-result-title {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-result-subtitle {
  color: rgba(255, 255, 255, 0.45);
}

.dark-theme .ant-form-item-label > label {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-form-item-explain,
.dark-theme .ant-form-item-extra {
  color: rgba(255, 255, 255, 0.45);
}

.dark-theme .ant-form-item-has-error .ant-input,
.dark-theme .ant-form-item-has-error .ant-input-affix-wrapper,
.dark-theme .ant-form-item-has-error .ant-input:hover,
.dark-theme .ant-form-item-has-error .ant-input-affix-wrapper:hover {
  background-color: #141414;
  border-color: #a61d24;
}

.dark-theme .ant-form-item-has-error .ant-form-item-explain,
.dark-theme .ant-form-item-has-error .ant-form-item-split {
  color: #a61d24;
}

.dark-theme .ant-badge-status-dot {
  background-color: #141414;
}

.dark-theme .ant-badge-status-success {
  background-color: #49aa19;
}

.dark-theme .ant-badge-status-error {
  background-color: #a61d24;
}

.dark-theme .ant-badge-status-default {
  background-color: #d9d9d9;
}

.dark-theme .ant-badge-status-processing {
  background-color: #177ddc;
}

.dark-theme .ant-badge-status-warning {
  background-color: #d89614;
}

.dark-theme .ant-divider {
  border-top-color: #303030;
}

.dark-theme .ant-divider-horizontal.ant-divider-with-text::before,
.dark-theme .ant-divider-horizontal.ant-divider-with-text::after {
  border-top-color: #303030;
}

.dark-theme .ant-divider-inner-text {
  color: rgba(255, 255, 255, 0.85);
}

.dark-theme .ant-tag {
  background: #1f1f1f;
  border-color: #434343;
}

.dark-theme .ant-tag-blue {
  color: #177ddc;
  background: #111b26;
  border-color: #15395b;
}

.dark-theme .ant-tag-green {
  color: #49aa19;
  background: #162312;
  border-color: #274916;
}

.dark-theme .ant-tag-red {
  color: #a61d24;
  background: #2a1215;
  border-color: #58181c;
}

.dark-theme .ant-tag-orange {
  color: #d89614;
  background: #2b2111;
  border-color: #594214;
}

.dark-theme .ant-tag-purple {
  color: #642ab5;
  background: #1a1325;
  border-color: #301c4d;
}

.dark-theme .ant-tag-cyan {
  color: #13a8a8;
  background: #112123;
  border-color: #144848;
}

.dark-theme .ant-tag-magenta {
  color: #cb2b83;
  background: #291321;
  border-color: #551c3b;
}