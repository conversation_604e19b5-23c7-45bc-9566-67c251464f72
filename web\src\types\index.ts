 // 代理节点类型
export interface ProxyNode {
    id: string;
    name: string;
    type: 'ss' | 'ssr' | 'vmess' | 'trojan' | 'hysteria' | 'hysteria2' | 'vless';
    server: string;
    port: number;
    country: string;
    region: string;
    city: string;
    latency: number;
    upload: number;
    download: number;
    status: 'active' | 'inactive' | 'testing';
    source: string;
    created_at: string;
    last_checked: string;
  }
  
  // 订阅源类型
  export interface Subscription {
    id: string;
    name: string;
    url: string;
    type: 'airport' | 'free' | 'custom';
    status: 'active' | 'inactive' | 'expired' | 'error';
    node_count: number;
    last_update: string;
    traffic_used: number;
    traffic_total: number;
    expire_date?: string;
    auto_renew: boolean;
    tags: string[];
  }
  
  // 机场账户类型
  export interface AirportAccount {
    id: string;
    name: string;
    domain: string;
    email: string;
    password: string;
    subscription_url: string;
    plan_id: number;
    plan_name: string;
    traffic_used: number;
    traffic_total: number;
    expire_date: string;
    auto_renew: boolean;
    renew_config: {
      coupon_code?: string;
      method: number;
    };
    status: 'active' | 'expired' | 'error';
    last_checked: string;
  }
  
  // 爬取任务类型
  export interface CrawlTask {
    id: string;
    name: string;
    type: 'telegram' | 'github' | 'twitter' | 'page' | 'script';
    config: Record<string, any>;
    status: 'running' | 'completed' | 'failed' | 'pending';
    progress: number;
    nodes_found: number;
    start_time: string;
    end_time?: string;
    error_message?: string;
  }
  
  // 系统统计类型
  export interface SystemStats {
    total_nodes: number;
    active_nodes: number;
    total_subscriptions: number;
    active_subscriptions: number;
    today_crawled: number;
    success_rate: number;
    avg_latency: number;
    countries: Array<{
      country: string;
      count: number;
    }>;
  }
  
  // 配置类型
  export interface Config {
    domains: Array<{
      name: string;
      domain: string;
      enable: boolean;
      renew?: {
        account: Array<{
          email: string;
          passwd: string;
        }>;
        plan_id: number;
      };
    }>;
    crawl: {
      enable: boolean;
      threshold: number;
      telegram: {
        enable: boolean;
        pages: number;
      };
      github: {
        enable: boolean;
        pages: number;
      };
      twitter: {
        enable: boolean;
      };
    };
    groups: Record<string, {
      targets: {
        clash?: string;
        v2ray?: string;
        singbox?: string;
      };
    }>;
    storage: {
      engine: string;
      base: string;
      domain: string;
    };
  }
  
  // API响应类型
  export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
  }
  
  // 转换任务类型
  export interface ConvertTask {
    id: string;
    input_url: string;
    target_format: string;
    status: 'processing' | 'completed' | 'failed';
    output_url?: string;
    created_at: string;
    completed_at?: string;
  }
  
  // 日志条目类型
  export interface LogEntry {
    id: string;
    level: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR';
    message: string;
    module: string;
    timestamp: string;
    details?: Record<string, any>;
  }

  // 系统状态类型
  export interface SystemStatus {
    status: string;
    uptime: string;
    version: string;
    python_version: string;
    python_full_version: string;
    platform: string;
    architecture: string;
    processor: string;
    memory_used: string;
    memory_total: string;
    memory_percent: string;
    cpu_usage: string;
    cpu_count: number;
    disk_usage: {
      used: string;
      total: string;
      percent: string;
    };
    python_executable: string;
    working_directory: string;
    last_update: string;
  }