import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Popconfirm,
  message,
  Modal,
  Row,
  Col,
  Typography,
  Progress,
  Tooltip,
  Drawer,
  Descriptions,
  Badge,
  Divider,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  ExportOutlined,
  FilterOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { nodesApi } from '../services/api';
import { ProxyNode } from '../types';

const { Option } = Select;
const { Title, Text } = Typography;

const NodesManagement: React.FC = () => {
  const [nodes, setNodes] = useState<ProxyNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filterCountry, setFilterCountry] = useState<string>('');
  const [filterType, setFilterType] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [testingNodes, setTestingNodes] = useState<Set<string>>(new Set());
  const [nodeDetailVisible, setNodeDetailVisible] = useState(false);
  const [selectedNode, setSelectedNode] = useState<ProxyNode | null>(null);

  // 模拟数据
  const mockNodes: ProxyNode[] = [
    {
      id: '1',
      name: '🇺🇸 美国-洛杉矶-01',
      type: 'vmess',
      server: '192.168.1.100',
      port: 443,
      country: '美国',
      region: '加利福尼亚',
      city: '洛杉矶',
      latency: 120,
      upload: 1024,
      download: 2048,
      status: 'active',
      source: 'https://example.com/subscribe',
      created_at: '2024-01-15 10:30:00',
      last_checked: '2024-01-21 14:20:00',
    },
    {
      id: '2',
      name: '🇯🇵 日本-东京-02',
      type: 'trojan',
      server: '192.168.1.101',
      port: 443,
      country: '日本',
      region: '关东',
      city: '东京',
      latency: 95,
      upload: 2048,
      download: 4096,
      status: 'active',
      source: 'https://example2.com/subscribe',
      created_at: '2024-01-15 11:45:00',
      last_checked: '2024-01-21 14:18:00',
    },
    {
      id: '3',
      name: '🇭🇰 香港-01',
      type: 'ss',
      server: '192.168.1.102',
      port: 8080,
      country: '香港',
      region: '香港',
      city: '香港',
      latency: 45,
      upload: 1024,
      download: 1024,
      status: 'inactive',
      source: 'https://example3.com/subscribe',
      created_at: '2024-01-15 12:00:00',
      last_checked: '2024-01-21 14:15:00',
    },
  ];

  useEffect(() => {
    fetchNodes();
  }, [pagination.current, pagination.pageSize, searchText, filterCountry, filterType, filterStatus]);

  const fetchNodes = async () => {
    setLoading(true);
    try {
      // 这里使用模拟数据，实际项目中调用API
      setNodes(mockNodes);
      setPagination(prev => ({ ...prev, total: mockNodes.length }));
      
      // const response = await nodesApi.getNodes({
      //   page: pagination.current,
      //   size: pagination.pageSize,
      //   search: searchText || undefined,
      //   country: filterCountry || undefined,
      //   type: filterType || undefined,
      //   status: filterStatus || undefined,
      // });
      // if (response.data.success) {
      //   setNodes(response.data.data!.nodes);
      //   setPagination(prev => ({ ...prev, total: response.data.data!.total }));
      // }
    } catch (error) {
      message.error('获取节点列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTestNode = async (nodeId: string) => {
    setTestingNodes(prev => new Set([...prev, nodeId]));
    try {
      // const response = await nodesApi.testNode(nodeId);
      // if (response.data.success) {
      //   message.success('节点测试完成');
      //   fetchNodes();
      // }
      // 模拟测试
      setTimeout(() => {
        message.success('节点测试完成');
        setTestingNodes(prev => {
          const newSet = new Set(prev);
          newSet.delete(nodeId);
          return newSet;
        });
      }, 2000);
    } catch (error) {
      message.error('节点测试失败');
      setTestingNodes(prev => {
        const newSet = new Set(prev);
        newSet.delete(nodeId);
        return newSet;
      });
    }
  };

  const handleTestSelectedNodes = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要测试的节点');
      return;
    }
    
    setTestingNodes(prev => new Set([...prev, ...selectedRowKeys.map(k => k.toString())]));
    try {
      // const response = await nodesApi.testNodes(selectedRowKeys.map(k => k.toString()));
      // if (response.data.success) {
      //   message.success(`开始测试 ${selectedRowKeys.length} 个节点`);
      //   fetchNodes();
      // }
      // 模拟批量测试
      setTimeout(() => {
        message.success(`${selectedRowKeys.length} 个节点测试完成`);
        setTestingNodes(new Set());
        setSelectedRowKeys([]);
      }, 3000);
    } catch (error) {
      message.error('批量测试失败');
      setTestingNodes(new Set());
    }
  };

  const handleDeleteNode = async (nodeId: string) => {
    try {
      // const response = await nodesApi.deleteNode(nodeId);
      // if (response.data.success) {
      //   message.success('节点删除成功');
      //   fetchNodes();
      // }
      message.success('节点删除成功');
      setNodes(prev => prev.filter(node => node.id !== nodeId));
    } catch (error) {
      message.error('节点删除失败');
    }
  };

  const handleDeleteSelectedNodes = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的节点');
      return;
    }
    
    try {
      // const response = await nodesApi.deleteNodes(selectedRowKeys.map(k => k.toString()));
      // if (response.data.success) {
      //   message.success(`成功删除 ${selectedRowKeys.length} 个节点`);
      //   fetchNodes();
      // }
      message.success(`成功删除 ${selectedRowKeys.length} 个节点`);
      setNodes(prev => prev.filter(node => !selectedRowKeys.includes(node.id)));
      setSelectedRowKeys([]);
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'error';
      case 'testing': return 'processing';
      default: return 'default';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'vmess': return 'blue';
      case 'trojan': return 'green';
      case 'ss': return 'orange';
      case 'ssr': return 'red';
      case 'hysteria': return 'purple';
      case 'hysteria2': return 'cyan';
      case 'vless': return 'magenta';
      default: return 'default';
    }
  };

  const getLatencyColor = (latency: number) => {
    if (latency < 100) return '#52c41a';
    if (latency < 300) return '#faad14';
    return '#ff4d4f';
  };

  const columns: ColumnsType<ProxyNode> = [
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            setSelectedNode(record);
            setNodeDetailVisible(true);
          }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type) => (
        <Tag color={getTypeColor(type)}>{type.toUpperCase()}</Tag>
      ),
    },
    {
      title: '服务器',
      dataIndex: 'server',
      key: 'server',
      width: 140,
      ellipsis: true,
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 80,
    },
    {
      title: '地区',
      key: 'location',
      width: 120,
      render: (_, record) => (
        <Space>
          <GlobalOutlined />
          <Text>{record.country}</Text>
        </Space>
      ),
    },
    {
      title: '延迟',
      dataIndex: 'latency',
      key: 'latency',
      width: 100,
      sorter: (a, b) => a.latency - b.latency,
      render: (latency) => (
        <Tag color={getLatencyColor(latency)}>
          {latency}ms
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status, record) => {
        const isTesting = testingNodes.has(record.id);
        if (isTesting) {
          return <Tag color="processing">测试中</Tag>;
        }
        return (
          <Badge
            status={status === 'active' ? 'success' : 'error'}
            text={status === 'active' ? '可用' : '不可用'}
          />
        );
      },
    },
    {
      title: '最后检查',
      dataIndex: 'last_checked',
      key: 'last_checked',
      width: 150,
      render: (time) => (
        <Text type="secondary">{time}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="测试节点">
            <Button
              type="text"
              icon={<PlayCircleOutlined />}
              loading={testingNodes.has(record.id)}
              onClick={() => handleTestNode(record.id)}
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedNode(record);
                setNodeDetailVisible(true);
              }}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个节点吗？"
            onConfirm={() => handleDeleteNode(record.id)}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={3}>节点管理</Title>
          <Text type="secondary">管理和监控所有代理节点</Text>
        </div>

        {/* 搜索和过滤 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6}>
            <Input
              placeholder="搜索节点名称或服务器"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="国家/地区"
              value={filterCountry}
              onChange={setFilterCountry}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="美国">美国</Option>
              <Option value="日本">日本</Option>
              <Option value="香港">香港</Option>
              <Option value="新加坡">新加坡</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="协议类型"
              value={filterType}
              onChange={setFilterType}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="vmess">VMess</Option>
              <Option value="trojan">Trojan</Option>
              <Option value="ss">Shadowsocks</Option>
              <Option value="ssr">ShadowsocksR</Option>
              <Option value="hysteria">Hysteria</Option>
              <Option value="vless">VLESS</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="状态"
              value={filterStatus}
              onChange={setFilterStatus}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="active">可用</Option>
              <Option value="inactive">不可用</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={6}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchNodes}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<FilterOutlined />}
                onClick={() => {
                  setSearchText('');
                  setFilterCountry('');
                  setFilterType('');
                  setFilterStatus('');
                }}
              >
                重置
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Text>已选择 {selectedRowKeys.length} 个节点</Text>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleTestSelectedNodes}
              >
                批量测试
              </Button>
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 个节点吗？`}
                onConfirm={handleDeleteSelectedNodes}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('导出功能开发中')}
              >
                导出选中
              </Button>
            </Space>
          </div>
        )}

        {/* 节点列表 */}
        <Table
          columns={columns}
          dataSource={nodes}
          rowKey="id"
          rowSelection={rowSelection}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 节点详情抽屉 */}
      <Drawer
        title="节点详情"
        placement="right"
        width={500}
        onClose={() => setNodeDetailVisible(false)}
        open={nodeDetailVisible}
      >
        {selectedNode && (
          <div>
            <Descriptions column={1} bordered>
              <Descriptions.Item label="节点名称">
                {selectedNode.name}
              </Descriptions.Item>
              <Descriptions.Item label="协议类型">
                <Tag color={getTypeColor(selectedNode.type)}>
                  {selectedNode.type.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="服务器地址">
                {selectedNode.server}
              </Descriptions.Item>
              <Descriptions.Item label="端口">
                {selectedNode.port}
              </Descriptions.Item>
              <Descriptions.Item label="地理位置">
                {selectedNode.country} - {selectedNode.region} - {selectedNode.city}
              </Descriptions.Item>
              <Descriptions.Item label="延迟">
                <Tag color={getLatencyColor(selectedNode.latency)}>
                  {selectedNode.latency}ms
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="上传速度">
                {selectedNode.upload} KB/s
              </Descriptions.Item>
              <Descriptions.Item label="下载速度">
                {selectedNode.download} KB/s
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Badge
                  status={selectedNode.status === 'active' ? 'success' : 'error'}
                  text={selectedNode.status === 'active' ? '可用' : '不可用'}
                />
              </Descriptions.Item>
              <Descriptions.Item label="来源订阅">
                <Text copyable ellipsis={{ tooltip: selectedNode.source }}>
                  {selectedNode.source}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {selectedNode.created_at}
              </Descriptions.Item>
              <Descriptions.Item label="最后检查">
                {selectedNode.last_checked}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Space style={{ width: '100%', justifyContent: 'center' }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={testingNodes.has(selectedNode.id)}
                onClick={() => handleTestNode(selectedNode.id)}
              >
                测试节点
              </Button>
              <Popconfirm
                title="确定要删除这个节点吗？"
                onConfirm={() => {
                  handleDeleteNode(selectedNode.id);
                  setNodeDetailVisible(false);
                }}
              >
                <Button danger icon={<DeleteOutlined />}>
                  删除节点
                </Button>
              </Popconfirm>
            </Space>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default NodesManagement;