#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from fastapi import APIRouter, HTTPException, Query, WebSocket, WebSocketDisconnect, BackgroundTasks
from pydantic import BaseModel

# 项目根目录
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入任务管理器
from app.core.task_manager import (
    get_task_manager, 
    TaskManager, 
    TaskInfo, 
    TaskConfig, 
    TaskType, 
    TaskStatus,
    create_collect_task,
    create_process_task,
    create_custom_task
)

# 尝试导入subscribe模块
try:
    # 修复导入路径 - subscribe模块的logger直接在根目录下
    from subscribe.logger import logger
    # 创建一个简单的Config类来兼容
    class Config:
        def __init__(self):
            self.config_dir = project_root / "subscribe" / "config"
            self.default_config_file = self.config_dir / "config.default.json"
            self.user_config_file = self.config_dir / "config.json"
            self._config = {}
            self._default_config = {}
            self._load_config()

        def _load_config(self):
            try:
                # 首先加载默认配置
                if self.default_config_file.exists():
                    with open(self.default_config_file, 'r', encoding='utf-8') as f:
                        self._default_config = json.load(f)
                        self._config = self._default_config.copy()

                # 然后加载用户配置，覆盖默认配置
                if self.user_config_file.exists():
                    with open(self.user_config_file, 'r', encoding='utf-8') as f:
                        user_config = json.load(f)
                        self._merge_config(self._config, user_config)

            except Exception as e:
                print(f"Warning: Could not load config file: {e}")
                # 如果加载失败，使用空配置
                self._config = {}
                self._default_config = {}

        def _merge_config(self, base_config, user_config):
            """递归合并配置"""
            for key, value in user_config.items():
                if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                    self._merge_config(base_config[key], value)
                else:
                    base_config[key] = value

        def get(self, key, default=None):
            """获取配置值，支持点号分隔的嵌套键"""
            if '.' in key:
                keys = key.split('.')
                value = self._config
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        return default
                return value
            return self._config.get(key, default)

        def validate_config(self):
            """验证配置的有效性"""
            errors = []

            # 检查必要的配置项
            required_sections = ['domains', 'crawl', 'groups', 'storage']
            for section in required_sections:
                if section not in self._config:
                    errors.append(f"Missing required section: {section}")

            return errors

    HAS_SUBSCRIBE = True
except ImportError as e:
    HAS_SUBSCRIBE = False
    print(f"Warning: Could not import subscribe module: {e}. Some features will be disabled.")

api_router = APIRouter()

# 响应模型
class ApiResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Optional[Any] = None
    error: Optional[str] = None

# 系统统计模型
class SystemStats(BaseModel):
    total_nodes: int
    active_nodes: int
    total_subscriptions: int
    active_subscriptions: int
    today_crawled: int
    success_rate: float
    avg_latency: float
    countries: List[Dict[str, Any]]

# 系统统计API
@api_router.get("/stats", response_model=ApiResponse)
async def get_stats():
    """获取系统统计信息"""
    try:
        # 模拟数据
        mock_stats = SystemStats(
            total_nodes=4000,
            active_nodes=3400,
            total_subscriptions=45,
            active_subscriptions=38,
            today_crawled=1234,
            success_rate=85.6,
            avg_latency=120.5,
            countries=[
                {"country": "美国", "count": 1250},
                {"country": "日本", "count": 980},
                {"country": "香港", "count": 756},
                {"country": "新加坡", "count": 432},
                {"country": "其他", "count": 582},
            ]
        )
        
        return ApiResponse(success=True, data=mock_stats)
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 图表数据API
@api_router.get("/stats/chart/{type}", response_model=ApiResponse)
async def get_chart_data(type: str, period: str = Query("24h")):
    """获取图表数据"""
    try:
        # 模拟数据
        if type == "nodes":
            data = [
                {"time": "00:00", "active": 3200, "total": 3800},
                {"time": "04:00", "active": 3250, "total": 3850},
                {"time": "08:00", "active": 3300, "total": 3900},
                {"time": "12:00", "active": 3350, "total": 3950},
                {"time": "16:00", "active": 3400, "total": 4000},
                {"time": "20:00", "active": 3450, "total": 4050},
            ]
        elif type == "latency":
            data = [
                {"time": "00:00", "latency": 120},
                {"time": "04:00", "latency": 95},
                {"time": "08:00", "latency": 140},
                {"time": "12:00", "latency": 110},
                {"time": "16:00", "latency": 85},
                {"time": "20:00", "latency": 130},
            ]
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported chart type: {type}")
        
        return ApiResponse(success=True, data=data)
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 环境变量API
@api_router.get("/system/environment", response_model=ApiResponse)
async def get_environment():
    """获取环境变量"""
    try:
        env_vars = {}
        
        # 如果subscribe模块可用，尝试从配置中获取
        if HAS_SUBSCRIBE:
            try:
                config = Config()
                env_vars = {
                    "GIST_PAT": os.environ.get("GIST_PAT", ""),
                    "GIST_LINK": os.environ.get("GIST_LINK", ""),
                    "CUSTOMIZE_LINK": os.environ.get("CUSTOMIZE_LINK", ""),
                    "SKIP_ALIVE_CHECK": os.environ.get("SKIP_ALIVE_CHECK", "false"),
                    "WORKFLOW_MODE": os.environ.get("WORKFLOW_MODE", "0"),
                    "ENABLE_SPECIAL_PROTOCOLS": os.environ.get("ENABLE_SPECIAL_PROTOCOLS", "false"),
                    "DEBUG": os.environ.get("DEBUG", "false"),
                }
            except Exception as e:
                print(f"Error loading config: {e}")
        
        # 如果无法从配置获取，使用环境变量
        if not env_vars:
            env_vars = {
                "GIST_PAT": os.environ.get("GIST_PAT", ""),
                "GIST_LINK": os.environ.get("GIST_LINK", ""),
                "CUSTOMIZE_LINK": os.environ.get("CUSTOMIZE_LINK", ""),
                "SKIP_ALIVE_CHECK": os.environ.get("SKIP_ALIVE_CHECK", "false"),
                "WORKFLOW_MODE": os.environ.get("WORKFLOW_MODE", "0"),
                "ENABLE_SPECIAL_PROTOCOLS": os.environ.get("ENABLE_SPECIAL_PROTOCOLS", "false"),
                "DEBUG": os.environ.get("DEBUG", "false"),
            }
        
        return ApiResponse(success=True, data=env_vars)
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 更新环境变量API
@api_router.put("/system/environment", response_model=ApiResponse)
async def update_environment(env: Dict[str, str]):
    """更新环境变量"""
    try:
        # 在实际应用中，这里会更新.env文件或系统环境变量
        # 这里只是模拟成功
        return ApiResponse(success=True, message="环境变量已更新")
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 系统状态API
@api_router.get("/system/status", response_model=ApiResponse)
async def get_system_status():
    """获取系统状态"""
    try:
        import platform
        import psutil
        import time
        from datetime import timedelta
        
        # 获取系统信息
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        uptime = str(timedelta(seconds=int(uptime_seconds)))
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_used = f"{memory.used / (1024**3):.1f}GB"
        memory_total = f"{memory.total / (1024**3):.1f}GB"
        memory_percent = f"{memory.percent:.1f}%"
        
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        
        status = {
            "status": "running",
            "uptime": uptime,
            "version": "1.0.0",
            "python_version": sys.version.split()[0],  # 只显示版本号
            "python_full_version": sys.version,
            "platform": platform.platform(),
            "architecture": platform.architecture()[0],
            "processor": platform.processor() or "Unknown",
            "memory_used": memory_used,
            "memory_total": memory_total,
            "memory_percent": memory_percent,
            "cpu_usage": f"{cpu_percent:.1f}%",
            "cpu_count": psutil.cpu_count(),
            "disk_usage": {
                "used": f"{psutil.disk_usage('/').used / (1024**3):.1f}GB",
                "total": f"{psutil.disk_usage('/').total / (1024**3):.1f}GB",
                "percent": f"{psutil.disk_usage('/').percent:.1f}%"
            },
            "python_executable": sys.executable,
            "working_directory": os.getcwd(),
            "last_update": datetime.now().isoformat(),
        }
        
        return ApiResponse(success=True, data=status)
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 健康检查API
@api_router.get("/health", response_model=ApiResponse)
async def health_check():
    """健康检查"""
    return ApiResponse(success=True, message="Service is healthy")


# ========================= 任务管理API =========================

# 任务请求模型
class TaskCreateRequest(BaseModel):
    task_type: TaskType
    name: str
    config: Dict[str, Any] = {}

class TaskExecuteRequest(BaseModel):
    all_config: bool = True
    overwrite: bool = False
    skip_check: bool = True
    skip_subconverter: bool = False
    num_threads: int = 64
    delay: int = 5000
    gist: str = ""
    key: str = ""
    server_config: str = ""
    timeout: int = 5000
    retry: int = 3

# 获取所有任务
@api_router.get("/tasks", response_model=ApiResponse)
async def get_tasks():
    """获取所有任务列表"""
    try:
        task_manager = get_task_manager()
        tasks = task_manager.get_all_tasks()
        
        # 转换为字典格式，便于JSON序列化
        tasks_data = []
        for task in tasks:
            task_dict = task.dict()
            # 处理datetime字段
            for field in ['created_at', 'started_at', 'completed_at']:
                if task_dict.get(field):
                    task_dict[field] = task_dict[field].isoformat()
            tasks_data.append(task_dict)
        
        return ApiResponse(success=True, data=tasks_data)
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 获取单个任务详情
@api_router.get("/tasks/{task_id}", response_model=ApiResponse)
async def get_task(task_id: str):
    """获取指定任务的详细信息"""
    try:
        task_manager = get_task_manager()
        task = task_manager.get_task(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 转换为字典格式
        task_dict = task.dict()
        for field in ['created_at', 'started_at', 'completed_at']:
            if task_dict.get(field):
                task_dict[field] = task_dict[field].isoformat()
                
        return ApiResponse(success=True, data=task_dict)
    except HTTPException:
        raise
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 创建任务
@api_router.post("/tasks", response_model=ApiResponse)
async def create_task(request: TaskCreateRequest):
    """创建新任务"""
    try:
        task_manager = get_task_manager()
        
        if request.task_type == TaskType.COLLECT:
            task_id = create_collect_task(
                name=request.name,
                **request.config
            )
        elif request.task_type == TaskType.PROCESS:
            task_id = create_process_task(
                name=request.name,
                **request.config
            )
        elif request.task_type == TaskType.CUSTOM:
            task_id = create_custom_task(
                name=request.name,
                **request.config
            )
        else:
            raise HTTPException(status_code=400, detail="不支持的任务类型")
        
        return ApiResponse(success=True, data={"task_id": task_id}, message="任务创建成功")
    except HTTPException:
        raise
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 执行任务
@api_router.post("/tasks/{task_id}/execute", response_model=ApiResponse)
async def execute_task(task_id: str, background_tasks: BackgroundTasks):
    """执行指定任务"""
    try:
        task_manager = get_task_manager()
        task = task_manager.get_task(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
            
        if task.status == TaskStatus.RUNNING:
            raise HTTPException(status_code=400, detail="任务正在运行中")
            
        # 获取任务配置
        config = None
        if task.task_type == TaskType.COLLECT:
            config = TaskConfig(
                task_type=TaskType.COLLECT,
                name=task.name,
                script_path="subscribe/collect.py",
                args=["--all", "--overwrite", "--skip"]
            )
        elif task.task_type == TaskType.PROCESS:
            config = TaskConfig(
                task_type=TaskType.PROCESS,
                name=task.name,
                script_path="subscribe/process.py",
                args=["--overwrite"]
            )
        elif task.task_type == TaskType.CUSTOM:
            # 对于自定义任务，检查是否有存储的配置（兼容旧任务）
            if task.script_path:
                # 获取项目根目录
                project_root = Path(__file__).parent.parent.parent.parent.parent
                # 强制设置工作目录为subscribe目录
                working_dir = str(project_root / "subscribe")
                
                # 新任务：使用存储的配置
                config = TaskConfig(
                    task_type=TaskType.CUSTOM,
                    name=task.name,
                    script_path=task.script_path,
                    args=getattr(task, 'args', []),
                    env_vars=getattr(task, 'env_vars', {}),
                    timeout=getattr(task, 'timeout', 300),
                    working_dir=working_dir  # 强制设置工作目录
                )
            else:
                # 旧任务：提示重新创建
                raise HTTPException(
                    status_code=400, 
                    detail="此自定义任务是旧版本创建的，缺少必要配置。请删除此任务并重新创建。"
                )
        
        if not config:
            raise HTTPException(status_code=400, detail="无法创建任务配置")
        
        # 在后台执行任务
        background_tasks.add_task(task_manager.execute_task, task_id, config)
        
        return ApiResponse(success=True, message="任务已开始执行")
    except HTTPException:
        raise
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 快速执行 - Collect任务
@api_router.post("/tasks/quick/collect", response_model=ApiResponse)
async def quick_collect(request: TaskExecuteRequest, background_tasks: BackgroundTasks):
    """快速创建并执行collect任务"""
    try:
        task_id = create_collect_task(
            name="快速代理收集",
            all_config=request.all_config,
            overwrite=request.overwrite,
            skip_check=request.skip_check,
            skip_subconverter=request.skip_subconverter,
            num=request.num_threads,
            delay=request.delay,
            gist=request.gist,
            key=request.key
        )
        
        task_manager = get_task_manager()
        config = TaskConfig(
            task_type=TaskType.COLLECT,
            name="快速代理收集",
            script_path="subscribe/collect.py",
            args=["--all", "--overwrite", "--skip"] if request.skip_check else ["--all", "--overwrite"]
        )
        
        # 在后台执行任务
        background_tasks.add_task(task_manager.execute_task, task_id, config)
        
        return ApiResponse(success=True, data={"task_id": task_id}, message="快速收集任务已开始执行")
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 快速执行 - Process任务
@api_router.post("/tasks/quick/process", response_model=ApiResponse)
async def quick_process(request: TaskExecuteRequest, background_tasks: BackgroundTasks):
    """快速创建并执行process任务"""
    try:
        task_id = create_process_task(
            name="快速代理处理",
            overwrite=request.overwrite,
            server_config=request.server_config,
            skip_subconverter=request.skip_subconverter,
            num=request.num_threads,
            timeout=request.timeout,
            retry=request.retry
        )
        
        task_manager = get_task_manager()
        config = TaskConfig(
            task_type=TaskType.PROCESS,
            name="快速代理处理",
            script_path="subscribe/process.py",
            args=["--overwrite"] if request.overwrite else []
        )
        
        # 在后台执行任务
        background_tasks.add_task(task_manager.execute_task, task_id, config)
        
        return ApiResponse(success=True, data={"task_id": task_id}, message="快速处理任务已开始执行")
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 取消任务
@api_router.post("/tasks/{task_id}/cancel", response_model=ApiResponse)
async def cancel_task(task_id: str):
    """取消指定任务"""
    try:
        task_manager = get_task_manager()
        success = task_manager.cancel_task(task_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="无法取消任务")
        
        return ApiResponse(success=True, message="任务已取消")
    except HTTPException:
        raise
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 删除任务
@api_router.delete("/tasks/{task_id}", response_model=ApiResponse)
async def delete_task(task_id: str):
    """删除指定任务"""
    try:
        task_manager = get_task_manager()
        success = task_manager.delete_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return ApiResponse(success=True, message="任务已删除")
    except HTTPException:
        raise
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# 获取任务日志
@api_router.get("/tasks/{task_id}/logs", response_model=ApiResponse)
async def get_task_logs(task_id: str, since: Optional[int] = Query(None)):
    """获取任务日志"""
    try:
        task_manager = get_task_manager()
        logs = task_manager.get_task_logs(task_id, since)
        
        return ApiResponse(success=True, data={"logs": logs})
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

# WebSocket实时日志
@api_router.websocket("/tasks/{task_id}/logs/stream")
async def task_logs_websocket(websocket: WebSocket, task_id: str):
    """WebSocket实时日志流"""
    await websocket.accept()
    
    task_manager = get_task_manager()
    log_queue = task_manager.get_log_stream(task_id)
    
    try:
        while True:
            # 检查是否有新日志
            try:
                log_message = log_queue.get_nowait()
                await websocket.send_json(log_message)
            except:
                # 队列为空，等待一下
                await asyncio.sleep(0.1)
                
            # 检查任务状态
            task = task_manager.get_task(task_id)
            if task and task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                # 发送状态更新
                await websocket.send_json({
                    'type': 'status_update',
                    'status': task.status,
                    'progress': task.progress,
                    'timestamp': datetime.now().isoformat()
                })
                
                if task.status != TaskStatus.RUNNING:
                    break
                    
    except WebSocketDisconnect:
        pass
    except Exception as e:
        await websocket.send_json({
            'type': 'error',
            'message': f"WebSocket错误: {str(e)}",
            'timestamp': datetime.now().isoformat()
        })


# ========================= 配置管理API =========================

# 获取配置文件
@api_router.get("/config", response_model=ApiResponse)
async def get_config():
    """获取系统配置"""
    try:
        if HAS_SUBSCRIBE:
            config = Config()
            # 验证配置
            validation_errors = config.validate_config()

            config_data = {
                "config": config._config,
                "default_config": config._default_config,
                "validation_errors": validation_errors,
                "is_valid": len(validation_errors) == 0
            }
            return ApiResponse(success=True, data=config_data)
        else:
            return ApiResponse(success=False, error="Subscribe模块不可用")
    except Exception as e:
        return ApiResponse(success=False, error=f"获取配置失败: {str(e)}")

# 更新配置文件
@api_router.put("/config", response_model=ApiResponse)
async def update_config(config_data: Dict[str, Any]):
    """更新系统配置"""
    try:
        if not HAS_SUBSCRIBE:
            return ApiResponse(success=False, error="Subscribe模块不可用")

        config_file = project_root / "subscribe" / "config" / "config.json"

        # 确保目录存在
        config_file.parent.mkdir(parents=True, exist_ok=True)

        # 验证配置数据
        try:
            # 创建临时Config实例来验证
            temp_config = Config()
            temp_config._config = config_data
            validation_errors = temp_config.validate_config()

            if validation_errors:
                return ApiResponse(
                    success=False,
                    error=f"配置验证失败: {'; '.join(validation_errors)}"
                )
        except Exception as e:
            return ApiResponse(success=False, error=f"配置验证异常: {str(e)}")

        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        return ApiResponse(success=True, message="配置已更新并验证通过")
    except Exception as e:
        return ApiResponse(success=False, error=f"更新配置失败: {str(e)}")

# 获取预设配置模板
@api_router.get("/config/templates", response_model=ApiResponse)
async def get_config_templates():
    """获取配置模板"""
    try:
        templates = {
            "collect": {
                "name": "代理收集模板",
                "description": "用于收集代理的基本配置",
                "config": {
                    "all_config": True,
                    "overwrite": False,
                    "skip_check": True,
                    "num_threads": 64,
                    "delay": 5000
                }
            },
            "process": {
                "name": "代理处理模板", 
                "description": "用于处理和验证代理的配置",
                "config": {
                    "overwrite": False,
                    "num_threads": 64,
                    "timeout": 5000,
                    "retry": 3
                }
            }
        }
        
        return ApiResponse(success=True, data=templates)
    except Exception as e:
        return ApiResponse(success=False, error=str(e))

