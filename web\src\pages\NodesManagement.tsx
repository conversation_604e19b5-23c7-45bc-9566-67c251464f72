import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Typography,
  Tooltip,
  Drawer,
  Descriptions,
  Badge,
  Divider,
  Statistic,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  ExportOutlined,
  FilterOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { nodesApi } from '../services/api';
import { ProxyNode } from '../types';

const { Option } = Select;
const { Title, Text } = Typography;

const NodesManagement: React.FC = () => {
  const [nodes, setNodes] = useState<ProxyNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filterCountry, setFilterCountry] = useState<string>('');
  const [filterType, setFilterType] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [testingNodes, setTestingNodes] = useState<Set<string>>(new Set());
  const [nodeDetailVisible, setNodeDetailVisible] = useState(false);
  const [selectedNode, setSelectedNode] = useState<ProxyNode | null>(null);

  // 节点统计数据
  const [nodeStats, setNodeStats] = useState({
    total_nodes: 0,
    active_nodes: 0,
    inactive_nodes: 0,
    country_distribution: [],
    type_distribution: []
  });

  useEffect(() => {
    fetchNodes();
    fetchNodeStats();
  }, [pagination.current, pagination.pageSize, searchText, filterCountry, filterType, filterStatus]);

  const fetchNodes = async () => {
    setLoading(true);
    try {
      const response = await nodesApi.getNodes({
        page: pagination.current,
        size: pagination.pageSize,
        search: searchText || undefined,
        country: filterCountry || undefined,
        type: filterType || undefined,
        status: filterStatus || undefined,
      });

      if (response.data.success) {
        setNodes(response.data.data!.nodes);
        setPagination(prev => ({
          ...prev,
          total: response.data.data!.total
        }));
      } else {
        message.error(response.data.error || '获取节点列表失败');
      }
    } catch (error) {
      console.error('获取节点列表失败:', error);
      message.error('获取节点列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchNodeStats = async () => {
    try {
      const response = await nodesApi.getNodeStats();
      if (response.data.success) {
        setNodeStats(response.data.data);
      }
    } catch (error) {
      console.error('获取节点统计失败:', error);
    }
  };

  const handleTestNode = async (nodeId: string) => {
    setTestingNodes(prev => new Set([...prev, nodeId]));
    try {
      const response = await nodesApi.testNode(nodeId);
      if (response.data.success) {
        const result = response.data.data;
        if (result.success) {
          message.success(`节点测试完成，延迟: ${result.latency}ms`);
        } else {
          message.warning(`节点测试失败: ${result.error}`);
        }
        fetchNodes(); // 刷新节点列表
      } else {
        message.error(response.data.error || '节点测试失败');
      }
    } catch (error) {
      console.error('节点测试失败:', error);
      message.error('节点测试失败');
    } finally {
      setTestingNodes(prev => {
        const newSet = new Set(prev);
        newSet.delete(nodeId);
        return newSet;
      });
    }
  };

  const handleTestSelectedNodes = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要测试的节点');
      return;
    }

    const nodeIds = selectedRowKeys.map(k => k.toString());
    setTestingNodes(prev => new Set([...prev, ...nodeIds]));

    try {
      const response = await nodesApi.testNodes(nodeIds);
      if (response.data.success) {
        const results = response.data.data.results;
        const successCount = results.filter((r: any) => r.success).length;
        const failCount = results.length - successCount;

        message.success(
          `批量测试完成：${successCount} 个成功，${failCount} 个失败`
        );
        fetchNodes(); // 刷新节点列表
        setSelectedRowKeys([]);
      } else {
        message.error(response.data.error || '批量测试失败');
      }
    } catch (error) {
      console.error('批量测试失败:', error);
      message.error('批量测试失败');
    } finally {
      setTestingNodes(new Set());
    }
  };

  const handleDeleteNode = async (nodeId: string) => {
    try {
      const response = await nodesApi.deleteNode(nodeId);
      if (response.data.success) {
        message.success('节点删除成功');
        fetchNodes(); // 刷新节点列表
      } else {
        message.error(response.data.error || '节点删除失败');
      }
    } catch (error) {
      console.error('节点删除失败:', error);
      message.error('节点删除失败');
    }
  };

  const handleDeleteSelectedNodes = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的节点');
      return;
    }

    try {
      const nodeIds = selectedRowKeys.map(k => k.toString());
      const response = await nodesApi.deleteNodes(nodeIds);
      if (response.data.success) {
        message.success(`成功删除 ${selectedRowKeys.length} 个节点`);
        fetchNodes(); // 刷新节点列表
        setSelectedRowKeys([]);
      } else {
        message.error(response.data.error || '批量删除失败');
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };



  const getTypeColor = (type: string) => {
    switch (type) {
      case 'vmess': return 'blue';
      case 'trojan': return 'green';
      case 'ss': return 'orange';
      case 'ssr': return 'red';
      case 'hysteria': return 'purple';
      case 'hysteria2': return 'cyan';
      case 'vless': return 'magenta';
      default: return 'default';
    }
  };

  const getLatencyColor = (latency: number) => {
    if (latency < 100) return '#52c41a';
    if (latency < 300) return '#faad14';
    return '#ff4d4f';
  };

  const columns: ColumnsType<ProxyNode> = [
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            setSelectedNode(record);
            setNodeDetailVisible(true);
          }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type) => (
        <Tag color={getTypeColor(type)}>{type.toUpperCase()}</Tag>
      ),
    },
    {
      title: '服务器',
      dataIndex: 'server',
      key: 'server',
      width: 140,
      ellipsis: true,
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 80,
    },
    {
      title: '地区',
      key: 'location',
      width: 120,
      render: (_, record) => (
        <Space>
          <GlobalOutlined />
          <Text>{record.country}</Text>
        </Space>
      ),
    },
    {
      title: '延迟',
      dataIndex: 'latency',
      key: 'latency',
      width: 100,
      sorter: (a, b) => a.latency - b.latency,
      render: (latency) => (
        <Tag color={getLatencyColor(latency)}>
          {latency}ms
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status, record) => {
        const isTesting = testingNodes.has(record.id);
        if (isTesting) {
          return <Tag color="processing">测试中</Tag>;
        }
        return (
          <Badge
            status={status === 'active' ? 'success' : 'error'}
            text={status === 'active' ? '可用' : '不可用'}
          />
        );
      },
    },
    {
      title: '最后检查',
      dataIndex: 'last_checked',
      key: 'last_checked',
      width: 150,
      render: (time) => (
        <Text type="secondary">{time}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="测试节点">
            <Button
              type="text"
              icon={<PlayCircleOutlined />}
              loading={testingNodes.has(record.id)}
              onClick={() => handleTestNode(record.id)}
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedNode(record);
                setNodeDetailVisible(true);
              }}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个节点吗？"
            onConfirm={() => handleDeleteNode(record.id)}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div>
      {/* 统计信息卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总节点数"
              value={nodeStats.total_nodes}
              prefix={<GlobalOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="可用节点"
              value={nodeStats.active_nodes}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="不可用节点"
              value={nodeStats.inactive_nodes}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="可用率"
              value={nodeStats.total_nodes > 0 ?
                ((nodeStats.active_nodes / nodeStats.total_nodes) * 100).toFixed(1) : 0}
              suffix="%"
              valueStyle={{
                color: nodeStats.total_nodes > 0 &&
                       (nodeStats.active_nodes / nodeStats.total_nodes) > 0.8 ?
                       '#3f8600' : '#cf1322'
              }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={3}>节点管理</Title>
          <Text type="secondary">管理和监控所有代理节点</Text>
        </div>

        {/* 搜索和过滤 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={6}>
            <Input
              placeholder="搜索节点名称或服务器"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="国家/地区"
              value={filterCountry}
              onChange={setFilterCountry}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="美国">美国</Option>
              <Option value="日本">日本</Option>
              <Option value="香港">香港</Option>
              <Option value="新加坡">新加坡</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="协议类型"
              value={filterType}
              onChange={setFilterType}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="vmess">VMess</Option>
              <Option value="trojan">Trojan</Option>
              <Option value="ss">Shadowsocks</Option>
              <Option value="ssr">ShadowsocksR</Option>
              <Option value="hysteria">Hysteria</Option>
              <Option value="vless">VLESS</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="状态"
              value={filterStatus}
              onChange={setFilterStatus}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="active">可用</Option>
              <Option value="inactive">不可用</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={6}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchNodes}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<FilterOutlined />}
                onClick={() => {
                  setSearchText('');
                  setFilterCountry('');
                  setFilterType('');
                  setFilterStatus('');
                }}
              >
                重置
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Text>已选择 {selectedRowKeys.length} 个节点</Text>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleTestSelectedNodes}
              >
                批量测试
              </Button>
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 个节点吗？`}
                onConfirm={handleDeleteSelectedNodes}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('导出功能开发中')}
              >
                导出选中
              </Button>
            </Space>
          </div>
        )}

        {/* 节点列表 */}
        <Table
          columns={columns}
          dataSource={nodes}
          rowKey="id"
          rowSelection={rowSelection}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 节点详情抽屉 */}
      <Drawer
        title="节点详情"
        placement="right"
        width={500}
        onClose={() => setNodeDetailVisible(false)}
        open={nodeDetailVisible}
      >
        {selectedNode && (
          <div>
            <Descriptions column={1} bordered>
              <Descriptions.Item label="节点名称">
                {selectedNode.name}
              </Descriptions.Item>
              <Descriptions.Item label="协议类型">
                <Tag color={getTypeColor(selectedNode.type)}>
                  {selectedNode.type.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="服务器地址">
                {selectedNode.server}
              </Descriptions.Item>
              <Descriptions.Item label="端口">
                {selectedNode.port}
              </Descriptions.Item>
              <Descriptions.Item label="地理位置">
                {selectedNode.country} - {selectedNode.region} - {selectedNode.city}
              </Descriptions.Item>
              <Descriptions.Item label="延迟">
                <Tag color={getLatencyColor(selectedNode.latency)}>
                  {selectedNode.latency}ms
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="上传速度">
                {selectedNode.upload} KB/s
              </Descriptions.Item>
              <Descriptions.Item label="下载速度">
                {selectedNode.download} KB/s
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Badge
                  status={selectedNode.status === 'active' ? 'success' : 'error'}
                  text={selectedNode.status === 'active' ? '可用' : '不可用'}
                />
              </Descriptions.Item>
              <Descriptions.Item label="来源订阅">
                <Text copyable ellipsis={{ tooltip: selectedNode.source }}>
                  {selectedNode.source}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {selectedNode.created_at}
              </Descriptions.Item>
              <Descriptions.Item label="最后检查">
                {selectedNode.last_checked}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Space style={{ width: '100%', justifyContent: 'center' }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={testingNodes.has(selectedNode.id)}
                onClick={() => handleTestNode(selectedNode.id)}
              >
                测试节点
              </Button>
              <Popconfirm
                title="确定要删除这个节点吗？"
                onConfirm={() => {
                  handleDeleteNode(selectedNode.id);
                  setNodeDetailVisible(false);
                }}
              >
                <Button danger icon={<DeleteOutlined />}>
                  删除节点
                </Button>
              </Popconfirm>
            </Space>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default NodesManagement;