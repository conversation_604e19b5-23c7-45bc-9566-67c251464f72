{
  " compilerOptions\: {
 \target\: \es2020\,
 \lib\: [
 \dom\,
 \dom.iterable\,
 \es2020\
 ],
 \allowJs\: true,
 \skipLibCheck\: true,
 \esModuleInterop\: true,
 \allowSyntheticDefaultImports\: true,
 \strict\: true,
 \forceConsistentCasingInFileNames\: true,
 \noFallthroughCasesInSwitch\: true,
 \module\: \esnext\,
 \moduleResolution\: \node\,
 \resolveJsonModule\: true,
 \isolatedModules\: true,
 \noEmit\: true,
 \jsx\: \react-jsx\,
 \incremental\: true,
 \tsBuildInfoFile\: \.tsbuildinfo\
 },
 \include\: [
 \src\
 ],
 \exclude\: [
 \node_modules\,
 \build\,
 \.tsbuildinfo\
 ]
}
