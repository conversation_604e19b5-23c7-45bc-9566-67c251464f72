import axios, { AxiosResponse } from 'axios';
import { 
  ProxyNode, 
  Subscription, 
  AirportAccount, 
  CrawlTask, 
  SystemStats, 
  Config, 
  ApiResponse,
  ConvertTask,
  LogEntry
} from '../types';

const API_BASE_URL = '/api/v1';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse<any>>) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 系统统计API
export const statsApi = {
  getStats: (): Promise<AxiosResponse<ApiResponse<SystemStats>>> =>
    apiClient.get('/stats'),
  
  getChartData: (type: string, period: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.get(`/stats/chart/${type}`, { params: { period } }),
};

// 代理节点API
export const nodesApi = {
  getNodes: (params?: {
    page?: number;
    size?: number;
    country?: string;
    type?: string;
    status?: string;
    search?: string;
  }): Promise<AxiosResponse<ApiResponse<{ nodes: ProxyNode[]; total: number }>>> =>
    apiClient.get('/nodes', { params }),
  
  getNode: (id: string): Promise<AxiosResponse<ApiResponse<ProxyNode>>> =>
    apiClient.get(`/nodes/${id}`),
  
  testNode: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post(`/nodes/${id}/test`),
  
  testNodes: (ids: string[]): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post('/nodes/test-batch', { node_ids: ids }),
  
  deleteNode: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.delete(`/nodes/${id}`),
  
  deleteNodes: (ids: string[]): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.delete('/nodes/batch', { data: { node_ids: ids } }),

  getNodeStats: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.get('/nodes/stats'),
};

// 订阅管理API
export const subscriptionsApi = {
  getSubscriptions: (): Promise<AxiosResponse<ApiResponse<Subscription[]>>> =>
    apiClient.get('/subscriptions'),
  
  addSubscription: (subscription: Partial<Subscription>): Promise<AxiosResponse<ApiResponse<Subscription>>> =>
    apiClient.post('/subscriptions', subscription),
  
  updateSubscription: (id: string, subscription: Partial<Subscription>): Promise<AxiosResponse<ApiResponse<Subscription>>> =>
    apiClient.put(`/subscriptions/${id}`, subscription),
  
  deleteSubscription: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.delete(`/subscriptions/${id}`),
  
  refreshSubscription: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post(`/subscriptions/${id}/refresh`),
  
  testSubscription: (url: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post('/subscriptions/test', { url }),
};

// 机场管理API
export const airportsApi = {
  getAccounts: (): Promise<AxiosResponse<ApiResponse<AirportAccount[]>>> =>
    apiClient.get('/airports'),
  
  addAccount: (account: Partial<AirportAccount>): Promise<AxiosResponse<ApiResponse<AirportAccount>>> =>
    apiClient.post('/airports', account),
  
  updateAccount: (id: string, account: Partial<AirportAccount>): Promise<AxiosResponse<ApiResponse<AirportAccount>>> =>
    apiClient.put(`/airports/${id}`, account),
  
  deleteAccount: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.delete(`/airports/${id}`),
  
  checkAccount: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post(`/airports/${id}/check`),
  
  renewAccount: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post(`/airports/${id}/renew`),
};

// 任务管理API
export const tasksApi = {
  // 获取所有任务
  getTasks: (): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    apiClient.get('/tasks'),
  
  // 获取单个任务详情
  getTask: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.get(`/tasks/${id}`),
    
  // 创建任务
  createTask: (data: {
    task_type: 'collect' | 'process' | 'custom';
    name: string;
    config?: any;
  }): Promise<AxiosResponse<ApiResponse<{ task_id: string }>>> =>
    apiClient.post('/tasks', data),
  
  // 执行任务
  executeTask: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post(`/tasks/${id}/execute`),
  
  // 取消任务
  cancelTask: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post(`/tasks/${id}/cancel`),
  
  // 删除任务
  deleteTask: (id: string): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.delete(`/tasks/${id}`),
  
  // 获取任务日志
  getTaskLogs: (id: string, since?: number): Promise<AxiosResponse<ApiResponse<{ logs: string[] }>>> =>
    apiClient.get(`/tasks/${id}/logs`, { params: { since } }),
  
  // 快速执行收集任务
  quickCollect: (params: {
    all_config?: boolean;
    overwrite?: boolean;
    skip_check?: boolean;
    num_threads?: number;
    delay?: number;
    gist?: string;
    key?: string;
  }): Promise<AxiosResponse<ApiResponse<{ task_id: string }>>> =>
    apiClient.post('/tasks/quick/collect', params),
  
  // 快速执行处理任务
  quickProcess: (params: {
    overwrite?: boolean;
    server_config?: string;
    num_threads?: number;
    timeout?: number;
    retry?: number;
  }): Promise<AxiosResponse<ApiResponse<{ task_id: string }>>> =>
    apiClient.post('/tasks/quick/process', params),
};

// 配置管理API
export const configApi = {
  // 获取配置
  getConfig: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.get('/config'),
    
  // 更新配置
  updateConfig: (config: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.put('/config', config),
    
  // 获取配置模板
  getTemplates: (): Promise<AxiosResponse<ApiResponse<Record<string, any>>>> =>
    apiClient.get('/config/templates'),
    
  // 获取环境变量
  getEnvironment: (): Promise<AxiosResponse<ApiResponse<Record<string, string>>>> =>
    apiClient.get('/system/environment'),
    
  // 更新环境变量
  updateEnvironment: (env: Record<string, string>): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.put('/system/environment', env),
    
  // 获取系统状态
  getSystemStatus: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.get('/system/status'),
};

// 转换工具API
export const convertApi = {
  convertSubscription: (data: {
    url: string;
    target: string;
    config?: any;
  }): Promise<AxiosResponse<ApiResponse<ConvertTask>>> =>
    apiClient.post('/convert', data),
  
  getConvertTask: (id: string): Promise<AxiosResponse<ApiResponse<ConvertTask>>> =>
    apiClient.get(`/convert/${id}`),
  
  getConvertResult: (id: string): Promise<AxiosResponse<string>> =>
    apiClient.get(`/convert/${id}/result`, { responseType: 'text' }),
  
  getSupportedFormats: (): Promise<AxiosResponse<ApiResponse<string[]>>> =>
    apiClient.get('/convert/formats'),
};

// 日志API
export const logsApi = {
  getLogs: (params?: {
    level?: string;
    module?: string;
    limit?: number;
    offset?: number;
  }): Promise<AxiosResponse<ApiResponse<{ logs: LogEntry[]; total: number }>>> =>
    apiClient.get('/logs', { params }),
  
  clearLogs: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.delete('/logs'),
};

// 系统API
export const systemApi = {
  getStatus: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.get('/system/status'),
  
  getEnvironment: (): Promise<AxiosResponse<ApiResponse<Record<string, string>>>> =>
    apiClient.get('/system/environment'),
  
  updateEnvironment: (env: Record<string, string>): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.put('/system/environment', env),
  
  restart: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    apiClient.post('/system/restart'),
  
  exportData: (type: string): Promise<AxiosResponse<Blob>> =>
    apiClient.get(`/system/export/${type}`, { responseType: 'blob' }),
};