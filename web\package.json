{"name": "aggregator-web", "version": "1.0.0", "description": "Web interface for Aggregator proxy pool", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.11", "antd": "^5.10.0", "axios": "^1.5.0", "monaco-editor": "^0.44.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-monaco-editor": "^0.55.0", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "recharts": "^2.8.0", "typescript": "^4.9.5", "web-vitals": "^3.4.0"}, "scripts": {"start": "cross-env GENERATE_SOURCEMAP=false DISABLE_ESLINT_PLUGIN=true DANGEROUSLY_DISABLE_HOST_CHECK=true react-scripts start", "start:fast": "cross-env GENERATE_SOURCEMAP=false DISABLE_ESLINT_PLUGIN=true TSC_COMPILE_ON_ERROR=true BROWSER=none react-scripts start", "start:debug": "cross-env DANGEROUSLY_DISABLE_HOST_CHECK=true react-scripts start", "build": "react-scripts build", "build:fast": "cross-env GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000", "devDependencies": {"cross-env": "^7.0.3"}}