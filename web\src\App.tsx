import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import MainLayout from './components/MainLayout';
import Dashboard from './pages/Dashboard';
import NodesManagement from './pages/NodesManagement';
import SubscriptionManagement from './pages/SubscriptionManagement';
import ConvertTool from './pages/ConvertTool';
import TaskMonitoring from './pages/TaskMonitoring';
import ConfigManagement from './pages/ConfigManagement';
import NotFound from './pages/NotFound';
import ErrorBoundary from './components/ErrorBoundary';
import './App.css';

const App: React.FC = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={{
          algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
          token: {
            colorPrimary: '#1890ff',
          },
        }}
      >
        <AntdApp>
          <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <MainLayout isDarkMode={isDarkMode} toggleTheme={toggleTheme}>
              <Routes>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/nodes" element={<NodesManagement />} />
                <Route path="/subscriptions" element={<SubscriptionManagement />} />
                <Route path="/convert" element={<ConvertTool />} />
                {/* 其他功能页面 */}
                <Route path="/tasks" element={<TaskMonitoring />} />
                <Route path="/config" element={<ConfigManagement />} />
                {/* 以下路由暂时使用NotFound组件 */}
                <Route path="/airports" element={<NotFound />} />
                <Route path="/settings" element={<NotFound />} />
                <Route path="/logs" element={<NotFound />} />
                {/* 捕获所有未匹配的路由 */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </MainLayout>
          </Router>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;