#!/bin/bash

echo "===================================="
echo "    快速启动 Aggregator Web 界面"
echo "===================================="

# 设置Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

# 设置优化环境变量
export GENERATE_SOURCEMAP=false
export DISABLE_ESLINT_PLUGIN=true
export TSC_COMPILE_ON_ERROR=true
export FAST_REFRESH=true
export BROWSER=none

echo "正在启动开发服务器（优化模式）..."
echo "提示：首次启动可能需要1-2分钟，后续启动会更快"
echo

# 启动开发服务器
npm run start:fast
