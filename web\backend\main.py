 #!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "subscribe"))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import uvicorn

from app.api.routes import api_router
from app.core.config import settings

# 创建FastAPI应用
app = FastAPI(
    title="Aggregator Web API",
    description="Aggregator代理池聚合器Web界面API",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json" if settings.DEBUG else None,
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 静态文件服务
if os.path.exists("../build"):
    app.mount("/static", StaticFiles(directory="../build/static"), name="static")
    
    @app.get("/{full_path:path}")
    async def serve_react_app(full_path: str):
        """为React应用提供服务"""
        if full_path.startswith("api/"):
            raise HTTPException(status_code=404)
        
        file_path = f"../build/{full_path}"
        if os.path.exists(file_path) and os.path.isfile(file_path):
            return FileResponse(file_path)
        else:
            return FileResponse("../build/index.html")

@app.get("/")
async def root():
    """根路径"""
    if os.path.exists("../build/index.html"):
        return FileResponse("../build/index.html")
    return {"message": "Aggregator Web API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "message": "Aggregator Web API is running"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=settings.DEBUG,
        log_level="info"
    )