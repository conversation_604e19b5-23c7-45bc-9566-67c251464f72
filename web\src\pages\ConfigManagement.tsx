import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Form,
  Input,
  Switch,
  Space,
  Typography,
  Row,
  Col,
  Tabs,
  Alert,
  Tooltip,
  Modal,
  Upload,
  Select,
  InputNumber,
  Divider,
  Tag,
  Table,
  Popconfirm,
  App,
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  DownloadOutlined,
  UploadOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import MonacoEditor from 'react-monaco-editor';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface EnvironmentVariable {
  key: string;
  value: string;
  description: string;
  required: boolean;
}

interface ConfigTemplate {
  name: string;
  description: string;
  config: any;
}

const ConfigManagement: React.FC = () => {
  const { message } = App.useApp();
  const [envForm] = Form.useForm();
  const [domainForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [configData, setConfigData] = useState<any>({});
  const [envVariables, setEnvVariables] = useState<Record<string, string>>({});
  const [jsonConfig, setJsonConfig] = useState<string>('{}');
  const [activeTab, setActiveTab] = useState('environment');
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [templates, setTemplates] = useState<Record<string, ConfigTemplate>>({});
  const [domainModalVisible, setDomainModalVisible] = useState(false);
  const [editingDomain, setEditingDomain] = useState<any>(null);

  // 环境变量配置
  const envConfig: EnvironmentVariable[] = [
    {
      key: 'GIST_PAT',
      value: '',
      description: 'GitHub个人访问令牌，用于推送订阅到Gist',
      required: true,
    },
    {
      key: 'GIST_LINK',
      value: '',
      description: 'GitHub Gist链接，格式：username/gist_id',
      required: true,
    },
    {
      key: 'CUSTOMIZE_LINK',
      value: '',
      description: '自定义机场链接，可以是URL或本地文件',
      required: false,
    },
    {
      key: 'SKIP_ALIVE_CHECK',
      value: 'false',
      description: '是否跳过节点可用性检查',
      required: false,
    },
    {
      key: 'WORKFLOW_MODE',
      value: '0',
      description: '工作流模式：0-正常模式，1-只爬取模式',
      required: false,
    },
    {
      key: 'ENABLE_SPECIAL_PROTOCOLS',
      value: 'false',
      description: '是否启用特殊协议(vless, hysteria2等)',
      required: false,
    },
    {
      key: 'SKIP_REMARK',
      value: 'false',
      description: '是否跳过备注处理',
      required: false,
    },
    {
      key: 'REACHABLE',
      value: 'true',
      description: '网络连接性检查',
      required: false,
    },
  ];

  useEffect(() => {
    fetchConfig();
    fetchEnvironment();
    fetchTemplates();
  }, []);

  const fetchConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/config');
      const data = await response.json();
      
      if (data.success) {
        setConfigData(data.data);
        setJsonConfig(JSON.stringify(data.data, null, 2));
      } else {
        message.error(data.error || '获取配置失败');
      }
    } catch (error) {
      console.error('获取配置失败:', error);
      message.error('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const fetchEnvironment = async () => {
    try {
      const response = await fetch('/api/v1/system/environment');
      const data = await response.json();
      
      if (data.success) {
        setEnvVariables(data.data);
        
        // 设置表单值
        const formValues: Record<string, any> = {};
        envConfig.forEach(env => {
          const value = data.data[env.key] || env.value;
          if (env.key === 'SKIP_ALIVE_CHECK' || env.key === 'ENABLE_SPECIAL_PROTOCOLS' || env.key === 'SKIP_REMARK') {
            formValues[env.key] = value === 'true';
          } else if (env.key === 'WORKFLOW_MODE') {
            formValues[env.key] = parseInt(value) || 0;
          } else {
            formValues[env.key] = value;
          }
        });
        
        envForm.setFieldsValue(formValues);
      }
    } catch (error) {
      console.error('获取环境变量失败:', error);
    }
  };

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/v1/config/templates');
      const data = await response.json();
      
      if (data.success) {
        setTemplates(data.data);
      }
    } catch (error) {
      console.error('获取模板失败:', error);
    }
  };

  const saveEnvironment = async (values: any) => {
    try {
      setLoading(true);
      
      // 转换boolean和number值为字符串
      const envData: Record<string, string> = {};
      Object.keys(values).forEach(key => {
        envData[key] = String(values[key]);
      });
      
      const response = await fetch('/api/v1/system/environment', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(envData),
      });
      
      const data = await response.json();
      
      if (data.success) {
        message.success('环境变量已保存');
        setEnvVariables(envData);
      } else {
        message.error(data.error || '保存失败');
      }
    } catch (error) {
      message.error('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const saveJsonConfig = async () => {
    try {
      setLoading(true);
      
      // 验证JSON格式
      let configObject;
      try {
        configObject = JSON.parse(jsonConfig);
      } catch (e) {
        message.error('JSON格式错误，请检查语法');
        return;
      }
      
      const response = await fetch('/api/v1/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(configObject),
      });
      
      const data = await response.json();
      
      if (data.success) {
        message.success('配置已保存');
        setConfigData(configObject);
      } else {
        message.error(data.error || '保存失败');
      }
    } catch (error) {
      message.error('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const loadTemplate = (templateKey: string) => {
    const template = templates[templateKey];
    if (template) {
      setJsonConfig(JSON.stringify(template.config, null, 2));
      message.success(`已加载模板：${template.name}`);
    }
  };

  const downloadConfig = () => {
    const dataStr = JSON.stringify(configData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `aggregator-config-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const config = JSON.parse(content);
        setJsonConfig(JSON.stringify(config, null, 2));
        message.success('配置文件已加载');
      } catch (error) {
        message.error('文件格式错误');
      }
    };
    reader.readAsText(file);
    return false; // 阻止自动上传
  };

  const addDomain = () => {
    setEditingDomain(null);
    setDomainModalVisible(true);
    domainForm.resetFields();
  };

  const editDomain = (domain: any, index: number) => {
    setEditingDomain({ ...domain, index });
    setDomainModalVisible(true);
    domainForm.setFieldsValue(domain);
  };

  const saveDomain = (values: any) => {
    try {
      const currentConfig = JSON.parse(jsonConfig);
      
      if (!currentConfig.domains) {
        currentConfig.domains = [];
      }
      
      if (editingDomain?.index !== undefined) {
        // 编辑现有域名
        currentConfig.domains[editingDomain.index] = values;
      } else {
        // 添加新域名
        currentConfig.domains.push(values);
      }
      
      setJsonConfig(JSON.stringify(currentConfig, null, 2));
      setDomainModalVisible(false);
      domainForm.resetFields();
      message.success(editingDomain ? '域名已更新' : '域名已添加');
    } catch (error) {
      message.error('配置格式错误');
    }
  };

  const deleteDomain = (index: number) => {
    try {
      const currentConfig = JSON.parse(jsonConfig);
      if (currentConfig.domains && currentConfig.domains.length > index) {
        currentConfig.domains.splice(index, 1);
        setJsonConfig(JSON.stringify(currentConfig, null, 2));
        message.success('域名已删除');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const domainColumns = [
    {
      title: '域名',
      dataIndex: 'domain',
      key: 'domain',
      ellipsis: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '启用',
      dataIndex: 'enable',
      key: 'enable',
      render: (enable: boolean) => (
        <Tag color={enable ? 'green' : 'red'}>
          {enable ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '订阅数',
      dataIndex: 'sub',
      key: 'sub',
      render: (subs: string[]) => subs?.length || 0,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: any, index: number) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => editDomain(record, index)}
          />
          <Popconfirm
            title="确定删除这个域名配置吗？"
            onConfirm={() => deleteDomain(index)}
          >
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>配置管理</Title>
        <Text type="secondary">管理系统环境变量和配置文件</Text>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        items={[
          {
            key: 'environment',
            label: '环境变量',
            children: (
              <Card
                title="环境变量配置"
                extra={
                  <Space>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={fetchEnvironment}
                      loading={loading}
                    >
                      刷新
                    </Button>
                  </Space>
                }
              >
                <Alert
                  message="环境变量说明"
                  description="这些环境变量控制着Aggregator的核心功能。请根据实际需求进行配置，必填项必须正确填写。"
                  type="info"
                  style={{ marginBottom: 24 }}
                  showIcon
                />
                
                <Form
                  form={envForm}
                  layout="vertical"
                  onFinish={saveEnvironment}
                >
                  <Row gutter={[16, 16]}>
                    {envConfig.map((env) => (
                      <Col xs={24} md={12} key={env.key}>
                        <Form.Item
                          name={env.key}
                          label={
                            <Space>
                              <Text strong>{env.key}</Text>
                              {env.required && <Tag color="red">必填</Tag>}
                              <Tooltip title={env.description}>
                                <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                              </Tooltip>
                            </Space>
                          }
                          rules={env.required ? [{ required: true, message: '此项为必填项' }] : []}
                        >
                          {env.key === 'SKIP_ALIVE_CHECK' ||
                          env.key === 'ENABLE_SPECIAL_PROTOCOLS' ||
                          env.key === 'SKIP_REMARK' ||
                          env.key === 'REACHABLE' ? (
                            <Switch
                              checkedChildren="启用"
                              unCheckedChildren="禁用"
                            />
                          ) : env.key === 'WORKFLOW_MODE' ? (
                            <Select>
                              <Option value={0}>正常模式</Option>
                              <Option value={1}>只爬取模式</Option>
                            </Select>
                          ) : env.key === 'GIST_PAT' ? (
                            <Input.Password placeholder={env.description} />
                          ) : (
                            <Input placeholder={env.description} />
                          )}
                        </Form.Item>
                      </Col>
                    ))}
                  </Row>
                  
                  <Divider />
                  
                  <Form.Item>
                    <Space>
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<SaveOutlined />}
                        loading={loading}
                      >
                        保存环境变量
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            )
          },
          {
            key: 'json',
            label: 'JSON配置',
            children: (
              <Card
                title="JSON配置文件"
                extra={
                  <Space>
                    <Button
                      icon={<UploadOutlined />}
                      onClick={() => setTemplateModalVisible(true)}
                    >
                      加载模板
                    </Button>
                    <Upload
                      accept=".json"
                      beforeUpload={handleUpload}
                      showUploadList={false}
                    >
                      <Button icon={<UploadOutlined />}>上传配置</Button>
                    </Upload>
                    <Button
                      icon={<DownloadOutlined />}
                      onClick={downloadConfig}
                    >
                      下载配置
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={fetchConfig}
                      loading={loading}
                    >
                      刷新
                    </Button>
                  </Space>
                }
              >
                <Alert
                  message="配置文件说明"
                  description="这是Aggregator的主配置文件，包含域名、爬取设置、存储配置等。请谨慎编辑，错误的配置可能导致程序无法正常运行。"
                  type="warning"
                  style={{ marginBottom: 16 }}
                  showIcon
                />
                
                <div style={{ height: 500, border: '1px solid #d9d9d9' }}>
                  <MonacoEditor
                    language="json"
                    value={jsonConfig}
                    onChange={setJsonConfig}
                    options={{
                      selectOnLineNumbers: true,
                      roundedSelection: false,
                      readOnly: false,
                      cursorStyle: 'line',
                      automaticLayout: true,
                      wordWrap: 'on',
                      minimap: { enabled: false },
                    }}
                  />
                </div>
                
                <div style={{ marginTop: 16 }}>
                  <Space>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      onClick={saveJsonConfig}
                      loading={loading}
                    >
                      保存配置
                    </Button>
                    <Button
                      icon={<EyeOutlined />}
                      onClick={() => {
                        try {
                          JSON.parse(jsonConfig);
                          message.success('JSON格式正确');
                        } catch (e) {
                          message.error('JSON格式错误');
                        }
                      }}
                    >
                      验证格式
                    </Button>
                  </Space>
                </div>
              </Card>
            )
          },
          {
            key: 'domains',
            label: '域名管理',
            children: (
              <Card
                title="域名配置管理"
                extra={
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={addDomain}
                  >
                    添加域名
                  </Button>
                }
              >
                <Alert
                  message="域名管理"
                  description="在这里可以可视化地管理机场域名配置，包括添加、编辑和删除域名配置。"
                  type="info"
                  style={{ marginBottom: 16 }}
                  showIcon
                />
                
                <Table
                  columns={domainColumns}
                  dataSource={configData.domains || []}
                  rowKey={(record, index) => `domain-${index}`}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                  }}
                />
              </Card>
            )
          }
        ]}
      />

      {/* 模板选择模态框 */}
      <Modal
        title="选择配置模板"
        open={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Row gutter={[16, 16]}>
          {Object.entries(templates).map(([key, template]) => (
            <Col xs={24} md={12} key={key}>
              <Card
                size="small"
                hoverable
                onClick={() => {
                  loadTemplate(key);
                  setTemplateModalVisible(false);
                }}
              >
                <Card.Meta
                  title={template.name}
                  description={template.description}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Modal>

      {/* 域名编辑模态框 */}
      <Modal
        title={editingDomain ? "编辑域名配置" : "添加域名配置"}
        open={domainModalVisible}
        onCancel={() => {
          setDomainModalVisible(false);
          domainForm.resetFields();
        }}
        onOk={() => domainForm.submit()}
        width={800}
      >
        <Form
          form={domainForm}
          layout="vertical"
          onFinish={saveDomain}
          initialValues={{
            enable: true,
            ignorede: true,
            liveness: true,
            rate: 2.5,
            count: 2,
            secure: false,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="domain"
                label="域名"
                rules={[{ required: true, message: '请输入域名' }]}
              >
                <Input placeholder="https://example.com" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="名称"
              >
                <Input placeholder="显示名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="enable" valuePropName="checked" label=" ">
                <Space>
                  <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                  <Text>启用状态</Text>
                </Space>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="liveness" valuePropName="checked" label=" ">
                <Space>
                  <Switch checkedChildren="检查" unCheckedChildren="跳过" />
                  <Text>活跃度检查</Text>
                </Space>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="secure" valuePropName="checked" label=" ">
                <Space>
                  <Switch checkedChildren="HTTPS" unCheckedChildren="HTTP" />
                  <Text>安全连接</Text>
                </Space>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="rate" label="评分">
                <InputNumber min={0} max={5} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="count" label="数量">
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="coupon" label="优惠券">
            <Input placeholder="优惠券代码（可选）" />
          </Form.Item>

          <Form.Item name="include" label="包含规则">
            <Input placeholder="包含的关键词，用|分隔" />
          </Form.Item>

          <Form.Item name="exclude" label="排除规则">
            <Input placeholder="排除的关键词，用|分隔" />
          </Form.Item>

          <Form.Item name="sub" label="订阅链接">
            <TextArea rows={3} placeholder="每行一个订阅链接" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ConfigManagement; 