@echo off
echo ====================================
echo    Aggregator Web 界面启动脚本
echo ====================================

REM 检查是否安装了Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到Node.js，请先安装Node.js (v14+)
    exit /b 1
)

REM 检查是否安装了npm
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到npm，请先安装npm
    exit /b 1
)

REM 检查是否安装了Python
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到Python，请先安装Python (v3.7+)
    exit /b 1
)

REM 检查是否安装了pip
where pip >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到pip，请先安装pip
    exit /b 1
)

echo 开始安装前端依赖...
call npm install --legacy-peer-deps
if %ERRORLEVEL% NEQ 0 (
    echo 警告: 前端依赖安装可能有问题，但将继续尝试启动
)

echo 开始构建前端...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 前端构建失败
    exit /b 1
)

echo 开始安装后端依赖...
cd backend
call pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo 警告: 后端依赖安装可能有问题，但将继续尝试启动
)

echo 启动后端服务...
cd ..
start /B python backend/main.py

echo Aggregator Web 界面已启动!
echo 请访问: http://localhost:8000
echo 按 Ctrl+C 停止服务

REM 保持脚本运行
pause 