#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}====================================${NC}"
echo -e "${BLUE}   Aggregator Web 界面启动脚本     ${NC}"
echo -e "${BLUE}====================================${NC}"

# 检查是否安装了Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}错误: 未找到Node.js，请先安装Node.js (v14+)${NC}"
    exit 1
fi

# 检查是否安装了npm
if ! command -v npm &> /dev/null; then
    echo -e "${RED}错误: 未找到npm，请先安装npm${NC}"
    exit 1
fi

# 检查是否安装了Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}错误: 未找到Python，请先安装Python (v3.7+)${NC}"
    exit 1
fi

# 确定Python命令
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

# 检查是否安装了pip
if ! command -v pip3 &> /dev/null && ! command -v pip &> /dev/null; then
    echo -e "${RED}错误: 未找到pip，请先安装pip${NC}"
    exit 1
fi

# 确定pip命令
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
else
    PIP_CMD="pip"
fi

echo -e "${GREEN}开始安装前端依赖...${NC}"
npm install --legacy-peer-deps

if [ $? -ne 0 ]; then
    echo -e "${YELLOW}警告: 前端依赖安装可能有问题，但将继续尝试启动${NC}"
fi

echo -e "${GREEN}开始构建前端...${NC}"
npm run build

if [ $? -ne 0 ]; then
    echo -e "${RED}错误: 前端构建失败${NC}"
    exit 1
fi

echo -e "${GREEN}开始安装后端依赖...${NC}"
cd backend
$PIP_CMD install -r requirements.txt

if [ $? -ne 0 ]; then
    echo -e "${YELLOW}警告: 后端依赖安装可能有问题，但将继续尝试启动${NC}"
fi

echo -e "${GREEN}启动后端服务...${NC}"
cd ..
$PYTHON_CMD backend/main.py &
BACKEND_PID=$!

echo -e "${GREEN}Aggregator Web 界面已启动!${NC}"
echo -e "${GREEN}请访问: http://localhost:8000${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"

# 捕获SIGINT信号(Ctrl+C)
trap "echo -e '${YELLOW}正在停止服务...${NC}'; kill $BACKEND_PID; exit 0" INT

# 保持脚本运行
wait $BACKEND_PID 